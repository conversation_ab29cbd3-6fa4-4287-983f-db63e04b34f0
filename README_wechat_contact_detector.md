# 微信联系人检测器 - 合并版本

## 概述

`wechat_contact_detector.py` 是一个合并了 `detection.py` 和 `friend.py` 功能的完整微信联系人检测脚本。该脚本专注于识别微信通讯录界面中的最后一个联系人，并提供多种识别方法和完整的调试功能。

## 主要功能

### 🎯 核心功能
- **自动检测微信窗口并截图**：智能识别微信主窗口，获取高质量截图
- **多种联系人识别方法**：
  - 轮廓检测（快速，无需外部依赖）
  - 模板匹配（基于图像特征）
  - OCR文字识别（EasyOCR + Tesseract）
- **专门的联系人列表检测**：重点识别最后一个联系人（通常是"联系人"分类）
- **智能点击执行**：自动点击识别到的联系人

### 🧹 优化特性
- **清理后的检测逻辑**：删除了无用的检测方法，只保留核心功能
- **绿色粗框标记**：调试模式下只标记选中的最后一个条目
- **智能过滤排序**：自动去重、按置信度排序
- **完整的调试支持**：保存调试图像和详细日志

## 安装依赖

```bash
# 基础依赖（必需）
pip install opencv-python numpy pyautogui pygetwindow

# OCR依赖（可选，用于文字识别）
pip install pytesseract easyocr

# 如果使用pytesseract，还需要安装Tesseract-OCR
# Windows: 下载并安装 https://github.com/UB-Mannheim/tesseract/wiki
# Linux: sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim
# macOS: brew install tesseract tesseract-lang
```

## 使用方法

### 基本用法

```bash
# 默认模式：清理后的检测测试
python wechat_contact_detector.py

# 联系人识别测试模式
python wechat_contact_detector.py contact

# 清理后检测测试模式
python wechat_contact_detector.py cleaned
```

### 运行模式说明

1. **默认模式（清理后检测）**
   - 专注于识别最后一个联系人
   - 只标记绿色粗框
   - 适合验证清理后的功能

2. **联系人识别测试模式**
   - 完整的联系人识别测试
   - 显示所有识别到的联系人
   - 测试点击功能

3. **清理后检测测试模式**
   - 与默认模式相同
   - 提供详细的测试报告

## 代码结构

### 主要类和方法

```python
class WeChatContactDetector:
    # 初始化和配置
    def __init__(self, debug_mode=True)
    def setup_logging(self)
    def ensure_screenshot_dir(self)
    
    # 微信窗口检测
    def find_wechat_windows(self)
    def capture_wechat_window(self)
    
    # 联系人识别（核心功能）
    def find_contacts_in_screenshot(self, screenshot)
    def _find_contacts_by_list_items(self, screenshot)  # 重点方法
    def _find_contacts_by_contour_optimized(self, screenshot)
    def _find_contacts_by_ocr_fast(self, screenshot)
    
    # 测试功能
    def test_cleaned_detection(self)
    def click_contact_by_image_recognition(self, contact_name=None)
```

### 关键改进

1. **合并优化**：
   - 将 `detection.py` 的测试逻辑整合到主类中
   - 保留 `friend.py` 的完整功能实现
   - 统一了日志和调试系统

2. **功能专注**：
   - 重点识别最后一个联系人（通常是"联系人"分类）
   - 简化了轮廓检测逻辑
   - 优化了调试图像输出

3. **代码清理**：
   - 删除了重复的检测方法
   - 统一了命名规范
   - 改进了错误处理

## 输出文件

### 调试图像
- `screenshots/selected_contact_*.png`：标记选中联系人的图像
- `screenshots/list_edges_*.png`：边缘检测调试图像
- `screenshots/contact_recognition_debug_*.png`：完整识别调试图像

### 日志文件
- `wechat_contact_detector_YYYYMMDD.log`：详细的运行日志

## 使用注意事项

1. **运行环境**：
   - 确保微信窗口已打开并可见
   - 建议在微信通讯录界面运行
   - Windows系统下测试效果最佳

2. **调试模式**：
   - 默认开启调试模式，会保存调试图像
   - 可以通过 `debug_mode=False` 关闭调试

3. **识别精度**：
   - 轮廓检测方法最快最稳定
   - OCR方法识别精度高但速度较慢
   - 建议在良好的显示条件下使用

## 故障排除

### 常见问题

1. **无法找到微信窗口**
   - 确保微信已启动并可见
   - 检查窗口标题是否为"微信"

2. **截图质量差**
   - 确保微信窗口完全可见
   - 避免窗口被其他程序遮挡
   - 检查显示缩放设置

3. **识别结果不准确**
   - 尝试不同的识别方法
   - 检查调试图像分析问题
   - 调整识别参数

### 调试建议

1. 查看 `screenshots/` 目录中的调试图像
2. 检查日志文件中的详细信息
3. 使用不同的运行模式进行测试

## 版本信息

- **版本**：3.0.0 (合并版)
- **创建时间**：2025-08-05
- **作者**：AI Assistant
- **基于**：detection.py + friend.py 的功能合并

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
