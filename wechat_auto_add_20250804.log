2025-08-04 14:38:10,812 - WeChatAutoAdd - INFO - 创建截图目录: screenshots
2025-08-04 14:38:10,812 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:38:11,831 - WeChatAutoAdd - INFO - 目标窗口: friend.py - 微信发送消息 - Visual Studio Code - 此文件存在 1 个问题
2025-08-04 14:38:11,834 - WeChatAutoAdd - INFO - 窗口位置: (-8, -8), 尺寸: 1936x1048
2025-08-04 14:38:12,065 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差33.32, 边缘比例0.0448
2025-08-04 14:38:12,129 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250804_143812.png
2025-08-04 14:38:12,131 - WeChatAutoAdd - INFO - ✅ 成功获取微信主窗口截图
2025-08-04 14:38:12,132 - WeChatAutoAdd - INFO - 🔍 开始在截图中识别联系人列表...
2025-08-04 14:38:12,132 - WeChatAutoAdd - INFO - 🔍 OCR识别区域: (96, 157) 尺寸: 1161x733
2025-08-04 14:38:12,145 - WeChatAutoAdd - ERROR - ❌ Tesseract OCR失败: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-08-04 14:39:25,536 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:39:26,541 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-08-04 14:39:26,542 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-08-04 14:39:26,643 - WeChatAutoAdd - INFO - ✅ 成功获取微信主窗口截图
2025-08-04 14:39:26,643 - WeChatAutoAdd - INFO - 🔍 开始在截图中识别联系人列表...
2025-08-04 14:39:26,644 - WeChatAutoAdd - INFO - 🔍 OCR识别区域: (36, 97) 尺寸: 435x454
2025-08-04 14:39:26,650 - WeChatAutoAdd - ERROR - ❌ Tesseract OCR失败: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-08-04 14:56:56,357 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:56:56,366 - WeChatAutoAdd - DEBUG - 找到微信窗口: friend.py - 微信发送消息 - Visual Studio Code - 1936x1048
2025-08-04 14:56:56,370 - WeChatAutoAdd - INFO - 共找到 1 个微信窗口
2025-08-04 14:56:56,372 - WeChatAutoAdd - WARNING - 未找到微信添加朋友窗口
2025-08-04 14:56:56,373 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:56:56,373 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:56:56,377 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:56:56,377 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:56:56,378 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:56:56,378 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:56:58,098 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:56:58,098 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:56:58,687 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:56:58,687 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:56:58,689 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:56:58,689 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:56:58,692 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:56:58,692 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:56:59,697 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:56:59,697 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:56:59,698 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:56:59,698 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:56:59,712 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:56:59,712 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:56:59,790 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:56:59,790 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:56:59,791 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:56:59,791 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:56:59,793 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:56:59,793 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:00,295 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:00,295 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:00,307 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:00,307 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:00,378 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:00,378 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:00,380 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:00,380 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:00,381 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:00,381 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:00,383 - WeChatAutoAdd - INFO - 🔍 开始使用图像识别点击联系人: 第一个联系人
2025-08-04 14:57:00,383 - WeChatAutoAdd - INFO - 🔍 开始使用图像识别点击联系人: 第一个联系人
2025-08-04 14:57:00,390 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:00,390 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:00,392 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:00,392 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:00,416 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:00,416 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:00,931 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:00,931 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:00,932 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:00,932 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:00,934 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:00,934 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:01,936 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:01,936 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:01,937 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:01,937 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:01,945 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:01,945 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:02,022 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:02,022 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:02,024 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:02,024 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:02,030 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:02,030 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:02,532 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:02,532 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:02,542 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:02,542 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:02,616 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:02,616 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:02,620 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:02,620 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:02,621 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:02,621 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:02,622 - WeChatAutoAdd - ERROR - ❌ 无法获取微信窗口截图
2025-08-04 14:57:02,622 - WeChatAutoAdd - ERROR - ❌ 无法获取微信窗口截图
2025-08-04 14:57:05,470 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:57:05,472 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:05,473 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:05,484 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:05,998 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:06,000 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:06,003 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:07,006 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:07,007 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:07,019 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:07,093 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:07,094 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:07,095 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:07,597 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:07,608 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:07,677 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:07,680 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:07,681 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:07,691 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:57:07,691 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:57:07,694 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:07,694 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:07,696 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:07,696 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:07,712 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:07,712 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:08,227 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:08,227 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:08,228 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:08,228 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:08,229 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:08,229 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:09,230 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:09,230 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:09,231 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:09,231 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:09,244 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:09,244 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:09,329 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:09,329 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:09,330 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:09,330 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:09,331 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:09,331 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:09,832 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:09,832 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:09,841 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:09,841 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:09,910 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:09,910 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:09,913 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:09,913 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:09,921 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:09,921 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:11,505 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:57:11,505 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:57:11,505 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:57:11,521 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:11,521 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:11,521 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:57:11,524 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:11,524 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:11,524 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:57:11,541 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:11,541 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:11,541 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:57:12,058 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:12,058 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:12,058 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:57:12,060 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:12,060 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:12,060 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:57:12,061 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:12,061 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:12,061 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:57:13,062 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:13,062 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:13,062 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:57:13,063 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:13,063 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:13,063 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:13,072 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:13,072 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:13,072 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:13,139 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:13,139 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:13,139 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:13,140 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:13,140 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:13,140 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:13,141 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:13,141 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:13,141 - WeChatAutoAdd - WARNING - ⚠️ 截图质量验证失败，尝试重新截图
2025-08-04 14:57:13,643 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:13,643 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:13,643 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:57:13,697 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:13,697 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:13,697 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:57:13,811 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:13,811 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:13,811 - WeChatAutoAdd - INFO - ✅ 客户端区域截图完成
2025-08-04 14:57:13,956 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:13,956 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:13,956 - WeChatAutoAdd - WARNING - ⚠️ 截图尺寸过小: 1x1
2025-08-04 14:57:14,028 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:14,028 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:57:14,028 - WeChatAutoAdd - ERROR - ❌ 微信窗口截图失败
2025-08-04 14:58:22,384 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:58:22,385 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:58:22,385 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:58:22,405 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:58:22,920 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:58:22,920 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:58:22,921 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:58:23,922 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:58:23,922 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:58:23,925 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 14:58:23,933 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:58:23,934 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 14:58:23,935 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 14:58:23,936 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 14:58:24,009 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 14:58:24,020 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 14:58:24,020 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 14:58:24,034 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_145824.png
2025-08-04 14:58:24,037 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:58:24,037 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 14:58:24,038 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:58:24,038 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 14:58:24,040 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:58:24,040 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 14:58:24,056 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:58:24,056 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 14:58:24,571 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:58:24,571 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 14:58:24,572 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:58:24,572 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 14:58:24,572 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:58:24,572 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 14:58:25,574 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:58:25,574 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 14:58:25,575 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:58:25,575 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 14:58:25,577 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 14:58:25,577 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 14:58:25,585 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:58:25,585 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 14:58:25,587 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 14:58:25,587 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 14:58:25,588 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 14:58:25,588 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 14:58:25,588 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 14:58:25,588 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 14:58:25,650 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 14:58:25,650 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 14:58:25,658 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 14:58:25,658 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 14:58:25,659 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 14:58:25,659 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 14:58:25,674 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_145825.png
2025-08-04 14:58:25,674 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_145825.png
2025-08-04 14:58:25,677 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 14:58:25,677 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 14:58:25,682 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 14:58:25,682 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 14:58:25,685 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 14:58:25,685 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 14:58:25,688 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 14:58:25,688 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 14:58:25,689 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 14:58:25,689 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 14:58:25,691 - WeChatAutoAdd - INFO - 🔍 联系人识别结果不足，尝试OCR补充...
2025-08-04 14:58:25,691 - WeChatAutoAdd - INFO - 🔍 联系人识别结果不足，尝试OCR补充...
2025-08-04 14:58:25,692 - WeChatAutoAdd - INFO - 🔍 开始快速OCR识别...
2025-08-04 14:58:25,692 - WeChatAutoAdd - INFO - 🔍 开始快速OCR识别...
2025-08-04 14:58:25,693 - WeChatAutoAdd - INFO - 📐 OCR识别区域: (35, 91) 尺寸: 426x428
2025-08-04 14:58:25,693 - WeChatAutoAdd - INFO - 📐 OCR识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:27,389 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:27,393 - WeChatAutoAdd - DEBUG - 找到微信窗口: friend.py - 微信发送消息 - Visual Studio Code - 1936x1048
2025-08-04 15:02:27,394 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-04 15:02:27,394 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-04 15:02:27,395 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-04 15:02:27,395 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-04 15:02:27,396 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-08-04 15:02:27,901 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-08-04 15:02:27,902 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-08-04 15:02:27,994 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差20.03, 边缘比例0.0176
2025-08-04 15:02:28,005 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250804_150227.png
2025-08-04 15:02:28,006 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-04 15:02:28,009 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-08-04 15:02:28,011 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-08-04 15:02:28,013 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-08-04 15:02:28,023 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250804_150228.png
2025-08-04 15:02:28,025 - WeChatAutoAdd - INFO - 底部区域原始检测到 7 个轮廓
2025-08-04 15:02:28,026 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-08-04 15:02:28,026 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-08-04 15:02:28,027 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-08-04 15:02:28,027 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-08-04 15:02:28,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-08-04 15:02:28,028 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-08-04 15:02:28,029 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-08-04 15:02:28,034 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-04 15:02:51,722 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:51,723 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:02:51,724 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:02:51,733 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:02:52,248 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:02:52,249 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:02:52,249 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:02:53,250 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:02:53,251 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:02:53,253 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:02:53,259 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:02:53,260 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:02:53,261 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:02:53,262 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:02:53,339 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:02:53,349 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:02:53,349 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:02:53,366 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_150253.png
2025-08-04 15:02:53,371 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,371 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,372 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:53,372 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:53,373 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,373 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,381 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,381 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,381 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,383 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:53,383 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:53,383 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:53,386 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,386 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,386 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,394 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,394 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,394 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,394 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:53,400 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:53,400 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:53,400 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:53,400 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:53,402 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,402 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,402 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,402 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,416 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:53,416 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:53,416 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:53,416 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:53,421 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,421 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,421 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:53,421 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:54,973 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:54,973 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:54,973 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:54,973 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:54,973 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:02:54,974 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:02:54,974 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:02:54,974 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:02:54,974 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:02:54,974 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:02:54,977 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:02:54,977 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:02:54,977 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:02:54,977 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:02:54,977 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:02:54,994 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:02:54,994 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:02:54,994 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:02:54,994 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:02:54,994 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:02:55,525 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:02:55,525 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:02:55,525 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:02:55,525 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:02:55,525 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:02:55,527 - WeChatAutoAdd - WARNING - ⚠️ 微信窗口激活但未在前台，尝试强制置前
2025-08-04 15:02:55,527 - WeChatAutoAdd - WARNING - ⚠️ 微信窗口激活但未在前台，尝试强制置前
2025-08-04 15:02:55,527 - WeChatAutoAdd - WARNING - ⚠️ 微信窗口激活但未在前台，尝试强制置前
2025-08-04 15:02:55,527 - WeChatAutoAdd - WARNING - ⚠️ 微信窗口激活但未在前台，尝试强制置前
2025-08-04 15:02:55,527 - WeChatAutoAdd - WARNING - ⚠️ 微信窗口激活但未在前台，尝试强制置前
2025-08-04 15:02:55,855 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:02:55,855 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:02:55,855 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:02:55,855 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:02:55,855 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:02:56,892 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:02:56,892 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:02:56,892 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:02:56,892 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:02:56,892 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:02:56,893 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:02:56,893 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:02:56,893 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:02:56,893 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:02:56,893 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:02:56,897 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:02:56,897 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:02:56,897 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:02:56,897 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:02:56,897 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:02:56,906 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:02:56,906 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:02:56,906 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:02:56,906 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:02:56,906 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:02:56,907 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:02:56,907 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:02:56,907 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:02:56,907 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:02:56,907 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:02:56,909 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:02:56,909 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:02:56,909 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:02:56,909 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:02:56,909 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:02:56,910 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:02:56,910 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:02:56,910 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:02:56,910 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:02:56,910 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:02:56,986 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:02:56,986 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:02:56,986 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:02:56,986 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:02:56,986 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:02:57,000 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:02:57,000 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:02:57,000 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:02:57,000 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:02:57,000 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:02:57,002 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:02:57,002 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:02:57,002 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:02:57,002 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:02:57,002 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:02:57,012 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_150257.png
2025-08-04 15:02:57,012 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_150257.png
2025-08-04 15:02:57,012 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_150257.png
2025-08-04 15:02:57,012 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_150257.png
2025-08-04 15:02:57,012 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_150257.png
2025-08-04 15:02:57,016 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:57,016 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:57,016 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:57,016 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:57,016 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测...
2025-08-04 15:02:57,020 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,020 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,020 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,020 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,020 - WeChatAutoAdd - INFO - 📐 通讯录识别区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,025 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:57,025 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:57,025 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:57,025 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:57,025 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:02:57,029 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,029 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,029 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,029 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:02:57,029 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:04:19,444 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:04:19,445 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:04:19,446 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:04:19,456 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:04:19,991 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:04:20,003 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:04:20,011 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:04:21,012 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:04:21,013 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:04:21,016 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:04:21,021 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:04:21,022 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:04:21,022 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:04:21,025 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:04:21,102 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:04:21,114 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:04:21,114 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:04:21,122 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_150421.png
2025-08-04 15:05:17,924 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:05:17,928 - WeChatAutoAdd - DEBUG - 找到微信窗口: friend.py - 微信发送消息 - Visual Studio Code - 1936x1048
2025-08-04 15:05:17,929 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-04 15:05:17,929 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-04 15:05:17,930 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-04 15:05:17,930 - WeChatAutoAdd - INFO - 共找到 4 个微信窗口
2025-08-04 15:05:17,931 - WeChatAutoAdd - INFO - 找到微信主窗口: 微信
2025-08-04 15:05:18,434 - WeChatAutoAdd - INFO - 目标窗口: 微信
2025-08-04 15:05:18,435 - WeChatAutoAdd - INFO - 窗口位置: (0, 0), 尺寸: 726x650
2025-08-04 15:05:18,587 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差20.03, 边缘比例0.0176
2025-08-04 15:05:18,658 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250804_150518.png
2025-08-04 15:05:18,659 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-04 15:05:18,661 - WeChatAutoAdd - INFO - 截图尺寸: 726x650
2025-08-04 15:05:18,662 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=405-650 (高度:245)
2025-08-04 15:05:18,662 - WeChatAutoAdd - INFO - 特别关注区域: Y=466-526 (距底部154像素)
2025-08-04 15:05:18,666 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250804_150518.png
2025-08-04 15:05:18,667 - WeChatAutoAdd - INFO - 底部区域原始检测到 7 个轮廓
2025-08-04 15:05:18,670 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,612), 尺寸17x1, 长宽比17.00, 面积17
2025-08-04 15:05:18,671 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,610), 尺寸18x1, 长宽比18.00, 面积18
2025-08-04 15:05:18,672 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,604), 尺寸18x3, 长宽比6.00, 面积54
2025-08-04 15:05:18,673 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(21,601), 尺寸18x1, 长宽比18.00, 面积18
2025-08-04 15:05:18,674 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,599), 尺寸17x1, 长宽比17.00, 面积17
2025-08-04 15:05:18,676 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(22,547), 尺寸16x22, 长宽比0.73, 面积352
2025-08-04 15:05:18,677 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,405), 尺寸726x245, 长宽比2.96, 面积177870
2025-08-04 15:05:18,679 - WeChatAutoAdd - WARNING - 底部区域未找到按钮
2025-08-04 15:11:50,863 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:11:50,864 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:11:50,864 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:11:50,877 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:11:51,579 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:11:51,580 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:11:51,580 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:11:52,581 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:11:52,582 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:11:52,586 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:11:52,593 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:11:52,595 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:11:52,597 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:11:52,598 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:11:52,674 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:11:52,686 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:11:52,686 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:11:52,698 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_151152.png
2025-08-04 15:11:52,702 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专注联系人文字，排除按钮）...
2025-08-04 15:11:52,704 - WeChatAutoAdd - INFO - 📐 联系人文字识别区域: (35, 110) 尺寸: 355x367
2025-08-04 15:11:52,707 - WeChatAutoAdd - INFO - ✅ 轮廓检测找到 0 个联系人文字（已排除按钮）
2025-08-04 15:11:52,708 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 15:11:52,710 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 15:11:52,722 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:11:52,722 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:11:52,723 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:11:52,723 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:11:52,723 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:11:52,723 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:11:52,738 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:11:52,738 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:11:53,249 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:11:53,249 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:11:53,249 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:11:53,249 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:11:53,250 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:11:53,250 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:11:54,251 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:11:54,251 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:11:54,252 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:11:54,252 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:11:54,255 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:11:54,255 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:11:54,261 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:11:54,261 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:11:54,263 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:11:54,263 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:11:54,264 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:11:54,264 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:11:54,265 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:11:54,265 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:11:54,329 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:11:54,329 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:11:54,339 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:11:54,339 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:11:54,339 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:11:54,339 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:11:54,353 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_151154.png
2025-08-04 15:11:54,353 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_151154.png
2025-08-04 15:11:54,355 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专注联系人文字，排除按钮）...
2025-08-04 15:11:54,355 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专注联系人文字，排除按钮）...
2025-08-04 15:11:54,357 - WeChatAutoAdd - INFO - 📐 联系人文字识别区域: (35, 110) 尺寸: 355x367
2025-08-04 15:11:54,357 - WeChatAutoAdd - INFO - 📐 联系人文字识别区域: (35, 110) 尺寸: 355x367
2025-08-04 15:11:54,360 - WeChatAutoAdd - INFO - ✅ 轮廓检测找到 0 个联系人文字（已排除按钮）
2025-08-04 15:11:54,360 - WeChatAutoAdd - INFO - ✅ 轮廓检测找到 0 个联系人文字（已排除按钮）
2025-08-04 15:12:27,716 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:12:27,717 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:12:27,717 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:12:27,723 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:12:28,236 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:12:28,236 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:12:28,236 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:12:29,237 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:12:29,237 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:12:29,240 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:12:29,247 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:12:29,248 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:12:29,248 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:12:29,249 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:12:29,323 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:12:29,334 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:12:29,335 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:12:29,344 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_151229.png
2025-08-04 15:13:20,064 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:13:20,064 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:13:20,064 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:13:20,110 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:13:20,623 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:13:20,624 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:13:20,624 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:13:21,625 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:13:21,625 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:13:21,628 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:13:21,632 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:13:21,633 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:13:21,633 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:13:21,634 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:13:21,703 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:13:21,713 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:13:21,714 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:13:21,722 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_151321.png
2025-08-04 15:13:21,727 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专注联系人文字，排除按钮）...
2025-08-04 15:13:21,728 - WeChatAutoAdd - INFO - 📐 联系人文字识别区域: (35, 110) 尺寸: 355x367
2025-08-04 15:13:21,732 - WeChatAutoAdd - INFO - ✅ 轮廓检测找到 0 个联系人文字（已排除按钮）
2025-08-04 15:14:43,730 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 15:14:43,730 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 15:14:43,731 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 15:14:43,777 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 15:14:44,292 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 15:14:44,293 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 15:14:44,293 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 15:14:45,294 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 15:14:45,295 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 15:14:45,298 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 15:14:45,305 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 15:14:45,306 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 15:14:45,306 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 15:14:45,307 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 15:14:45,379 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 15:14:45,389 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 15:14:45,391 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 15:14:45,400 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_151445.png
2025-08-04 15:14:45,406 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专注联系人文字，排除按钮）...
2025-08-04 15:14:45,407 - WeChatAutoAdd - INFO - 📐 联系人文字识别区域: (35, 110) 尺寸: 355x367
2025-08-04 15:14:45,411 - WeChatAutoAdd - INFO - ✅ 轮廓检测找到 0 个联系人文字（已排除按钮）
2025-08-04 22:56:34,213 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 22:56:34,214 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 22:56:34,214 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 22:56:36,029 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 22:56:36,711 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 22:56:36,711 - WeChatAutoAdd - WARNING - ⚠️ 微信窗口激活但未在前台，尝试强制置前
2025-08-04 22:56:36,712 - WeChatAutoAdd - ERROR - ❌ 激活微信窗口异常: (0, 'SetForegroundWindow', 'No error message is available')
2025-08-04 22:56:36,715 - WeChatAutoAdd - ERROR - ❌ 备选激活方案失败: Error code from Windows: 0 - 操作成功完成。
2025-08-04 22:56:36,716 - WeChatAutoAdd - ERROR - ❌ 无法激活微信窗口
2025-08-04 22:57:50,691 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 22:57:50,692 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专注联系人文字，排除按钮）...
2025-08-04 22:57:50,692 - WeChatAutoAdd - INFO - 📐 联系人文字识别区域: (14, 30) 尺寸: 496x306
2025-08-04 22:57:50,698 - WeChatAutoAdd - INFO - 🔍 找到 6 个轮廓
2025-08-04 22:57:50,698 - WeChatAutoAdd - DEBUG - 轮廓1: 位置(0, 222) 尺寸6x12
2025-08-04 22:57:50,698 - WeChatAutoAdd - DEBUG - 轮廓2: 位置(5, 177) 尺寸6x6
2025-08-04 22:57:50,699 - WeChatAutoAdd - DEBUG - 轮廓3: 位置(0, 177) 尺寸4x4
2025-08-04 22:57:50,699 - WeChatAutoAdd - DEBUG - 轮廓4: 位置(0, 129) 尺寸15x11
2025-08-04 22:57:50,700 - WeChatAutoAdd - DEBUG - 轮廓5: 位置(2, 124) 尺寸12x7
2025-08-04 22:57:50,700 - WeChatAutoAdd - DEBUG - 轮廓6: 位置(0, 0) 尺寸496x306
2025-08-04 22:57:50,700 - WeChatAutoAdd - INFO - 📊 轮廓分析统计: 总轮廓6 -> 通过尺寸过滤0 -> 最终联系人0
2025-08-04 22:57:50,701 - WeChatAutoAdd - INFO - ✅ 轮廓检测找到 0 个联系人文字（专注窗口上部分）
2025-08-04 22:57:50,702 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 22:57:50,702 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:14:37,067 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:14:37,067 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:14:37,068 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:14:37,132 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:14:37,664 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:14:37,664 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:14:37,664 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:14:38,665 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:14:38,665 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:14:38,668 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:14:38,673 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:14:38,673 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:14:38,674 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:14:38,674 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:14:38,767 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:14:38,775 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:14:38,775 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:14:38,789 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_231438.png
2025-08-04 23:14:38,792 - WeChatAutoAdd - INFO - 🔧 开始调试模式轮廓检测（宽松条件）...
2025-08-04 23:14:38,795 - WeChatAutoAdd - INFO - 📐 调试识别区域: (0, 0) 尺寸: 568x489
2025-08-04 23:14:38,815 - WeChatAutoAdd - INFO - 🔍 binary方法找到 12 个轮廓
2025-08-04 23:14:38,820 - WeChatAutoAdd - INFO - 🔍 adaptive_mean方法找到 17 个轮廓
2025-08-04 23:14:38,824 - WeChatAutoAdd - INFO - 🔍 adaptive_gaussian方法找到 18 个轮廓
2025-08-04 23:14:38,832 - WeChatAutoAdd - INFO - 🔍 otsu方法找到 8 个轮廓
2025-08-04 23:14:38,833 - WeChatAutoAdd - INFO - ✅ 最佳方法: adaptive_gaussian，找到 18 个轮廓
2025-08-04 23:14:38,834 - WeChatAutoAdd - INFO - 🔍 轮廓4: 位置(26, 15) 尺寸13x13 面积50 置信度56
2025-08-04 23:14:38,834 - WeChatAutoAdd - INFO - 🔍 轮廓11: 位置(264, 0) 尺寸304x489 面积147864 置信度100
2025-08-04 23:14:38,834 - WeChatAutoAdd - INFO - 🔍 轮廓12: 位置(68, 0) 尺寸180x23 面积3923 置信度100
2025-08-04 23:14:38,836 - WeChatAutoAdd - INFO - 🔍 轮廓13: 位置(53, 0) 尺寸210x489 面积97377 置信度100
2025-08-04 23:14:38,838 - WeChatAutoAdd - INFO - 🔍 轮廓16: 位置(7, 0) 尺寸11x20 面积80 置信度61
2025-08-04 23:14:38,839 - WeChatAutoAdd - INFO - 🔍 轮廓18: 位置(0, 0) 尺寸52x489 面积23787 置信度100
2025-08-04 23:14:38,840 - WeChatAutoAdd - INFO - ✅ 调试模式找到 6 个候选联系人
2025-08-04 23:14:38,862 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专注联系人文字，排除按钮）...
2025-08-04 23:14:38,864 - WeChatAutoAdd - INFO - 📐 联系人文字识别区域: (14, 30) 尺寸: 496x306
2025-08-04 23:14:38,867 - WeChatAutoAdd - INFO - 🔍 找到 6 个轮廓
2025-08-04 23:14:38,868 - WeChatAutoAdd - DEBUG - 轮廓1: 位置(0, 222) 尺寸6x12
2025-08-04 23:14:38,869 - WeChatAutoAdd - DEBUG - 轮廓2: 位置(5, 177) 尺寸6x6
2025-08-04 23:14:38,869 - WeChatAutoAdd - DEBUG - 轮廓3: 位置(0, 177) 尺寸4x4
2025-08-04 23:14:38,870 - WeChatAutoAdd - DEBUG - 轮廓4: 位置(0, 129) 尺寸15x11
2025-08-04 23:14:38,870 - WeChatAutoAdd - DEBUG - 轮廓5: 位置(2, 124) 尺寸12x7
2025-08-04 23:14:38,870 - WeChatAutoAdd - DEBUG - 轮廓6: 位置(0, 0) 尺寸496x306
2025-08-04 23:14:38,872 - WeChatAutoAdd - INFO - 📊 轮廓分析统计: 总轮廓6 -> 通过尺寸过滤0 -> 最终联系人0
2025-08-04 23:14:38,873 - WeChatAutoAdd - INFO - ✅ 轮廓检测找到 0 个联系人文字（专注窗口上部分）
2025-08-04 23:14:38,890 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:14:38,891 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:17:51,061 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:17:51,062 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:17:51,062 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:17:51,107 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:17:51,622 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:17:51,623 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:17:51,623 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:17:52,624 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:17:52,624 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:17:52,628 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:17:52,635 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:17:52,636 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:17:52,636 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:17:52,638 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:17:52,709 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:17:52,720 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:17:52,721 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:17:52,728 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_231752.png
2025-08-04 23:17:52,734 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:17:52,734 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:17:52,736 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:17:52,737 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:17:52,737 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:17:52,740 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:17:52,741 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:20:22,346 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:20:22,346 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:20:22,346 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:20:22,399 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:20:22,912 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:20:22,913 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:20:22,913 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:20:23,914 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:20:23,914 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:20:23,918 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:20:23,927 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:20:23,927 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:20:23,928 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:20:23,928 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:20:24,002 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:20:24,013 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:20:24,014 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:20:24,025 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_232024.png
2025-08-04 23:20:24,028 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:20:24,030 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:20:24,031 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:20:24,032 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:20:24,033 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:20:24,034 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:20:24,034 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:20:24,035 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:20:24,035 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:20:24,036 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:20:24,036 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:20:24,037 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（基于成功方法改进）...
2025-08-04 23:20:24,041 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:20:24,041 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:20:24,044 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:20:24,045 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:20:24,048 - WeChatAutoAdd - INFO - 🔄 改进方法未找到联系人，回退到简单方法...
2025-08-04 23:20:24,049 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:20:24,051 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:20:24,052 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:20:24,053 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:20:24,053 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:20:24,054 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:20:24,054 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:20:24,059 - WeChatAutoAdd - INFO - ✅ 简单方法过滤后找到 0 个联系人
2025-08-04 23:20:24,062 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:20:24,062 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:20:24,065 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:20:24,065 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:20:24,066 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:20:24,066 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:20:24,070 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:20:24,070 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:20:24,582 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:20:24,582 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:20:24,582 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:20:24,582 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:20:24,583 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:20:24,583 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:20:25,583 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:20:25,583 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:20:25,584 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:20:25,584 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:20:25,587 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:20:25,587 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:20:25,595 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:20:25,595 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:20:25,596 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:20:25,596 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:20:25,597 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:20:25,597 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:20:25,598 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:20:25,598 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:20:25,660 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:20:25,660 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:20:25,668 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:20:25,668 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:20:25,669 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:20:25,669 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:20:25,680 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_232025.png
2025-08-04 23:20:25,680 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_232025.png
2025-08-04 23:20:25,683 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 23:20:25,683 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 23:20:25,684 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（基于成功方法改进）...
2025-08-04 23:20:25,684 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（基于成功方法改进）...
2025-08-04 23:20:25,685 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:20:25,685 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:20:25,685 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:20:25,685 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:20:25,687 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:20:25,687 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:20:25,693 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:20:25,693 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:20:25,694 - WeChatAutoAdd - INFO - 🔄 改进方法未找到联系人，回退到简单方法...
2025-08-04 23:20:25,694 - WeChatAutoAdd - INFO - 🔄 改进方法未找到联系人，回退到简单方法...
2025-08-04 23:20:25,695 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:20:25,695 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:20:25,696 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:20:25,696 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:20:25,698 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:20:25,698 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:20:25,700 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:20:25,700 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:20:25,700 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:20:25,700 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:20:25,701 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:20:25,701 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:20:25,702 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:20:25,702 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:20:25,702 - WeChatAutoAdd - INFO - ✅ 简单方法过滤后找到 0 个联系人
2025-08-04 23:20:25,702 - WeChatAutoAdd - INFO - ✅ 简单方法过滤后找到 0 个联系人
2025-08-04 23:20:25,703 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:20:25,703 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:20:25,709 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:20:25,709 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:20:25,711 - WeChatAutoAdd - INFO - 🔍 联系人识别结果不足，尝试OCR补充...
2025-08-04 23:20:25,711 - WeChatAutoAdd - INFO - 🔍 联系人识别结果不足，尝试OCR补充...
2025-08-04 23:20:25,712 - WeChatAutoAdd - INFO - 🔍 开始快速OCR识别...
2025-08-04 23:20:25,712 - WeChatAutoAdd - INFO - 🔍 开始快速OCR识别...
2025-08-04 23:20:25,714 - WeChatAutoAdd - INFO - 📐 OCR识别区域: (35, 91) 尺寸: 426x428
2025-08-04 23:20:25,714 - WeChatAutoAdd - INFO - 📐 OCR识别区域: (35, 91) 尺寸: 426x428
2025-08-04 23:42:03,502 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:42:03,503 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:42:03,503 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:42:04,678 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:42:05,210 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:42:05,211 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:42:05,211 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:42:06,212 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:42:06,212 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:42:06,215 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:42:06,224 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:42:06,225 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:42:06,225 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:42:06,226 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:42:06,301 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:42:06,313 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:42:06,313 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:42:06,327 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_234206.png
2025-08-04 23:42:06,331 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:42:06,332 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:42:06,334 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:42:06,347 - WeChatAutoAdd - INFO - 💾 保存轮廓标记图像: screenshots/contour_marked_20250804_234206.png
2025-08-04 23:42:06,350 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:42:06,350 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:42:06,351 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:42:06,351 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:42:06,352 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:42:06,352 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:42:06,353 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:42:06,362 - WeChatAutoAdd - INFO - 💾 保存改进轮廓标记图像: screenshots/improved_contour_marked_20250804_234206.png
2025-08-04 23:42:06,363 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:42:06,372 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:42:06,372 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:42:06,373 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:42:06,373 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:42:06,373 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:42:06,373 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:42:06,384 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:42:06,384 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:42:06,892 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:42:06,892 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:42:06,893 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:42:06,893 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:42:06,894 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:42:06,894 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:42:07,894 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:42:07,894 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:42:07,895 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:42:07,895 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:42:07,898 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:42:07,898 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:42:07,903 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:42:07,903 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:42:07,904 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:42:07,904 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:42:07,905 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:42:07,905 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:42:07,906 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:42:07,906 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:42:07,978 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:42:07,978 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:42:07,988 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:42:07,988 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:42:07,988 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:42:07,988 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:42:08,006 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_234207.png
2025-08-04 23:42:08,006 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_234207.png
2025-08-04 23:46:38,937 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:46:38,938 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:46:38,938 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:46:39,550 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:46:40,058 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:46:40,062 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:46:40,066 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:46:41,067 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:46:41,068 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:46:41,072 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:46:41,081 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:46:41,082 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:46:41,083 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:46:41,084 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:46:41,154 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:46:41,164 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:46:41,164 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:46:41,177 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_234641.png
2025-08-04 23:46:41,179 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:46:41,180 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:46:41,189 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:46:41,202 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_234641.png
2025-08-04 23:46:41,203 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 0 个条目
2025-08-04 23:46:41,204 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:46:41,204 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:46:41,205 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:46:41,215 - WeChatAutoAdd - INFO - 💾 保存轮廓标记图像: screenshots/contour_marked_20250804_234641.png
2025-08-04 23:46:41,217 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:46:41,217 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:46:41,218 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:46:41,218 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:46:41,219 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:46:41,219 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:46:41,220 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:46:41,228 - WeChatAutoAdd - INFO - 💾 保存改进轮廓标记图像: screenshots/improved_contour_marked_20250804_234641.png
2025-08-04 23:46:41,233 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:46:41,234 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专门针对联系人列表）...
2025-08-04 23:46:41,236 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:46:41,237 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:46:41,246 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:46:41,254 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_234641.png
2025-08-04 23:46:41,254 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 0 个条目
2025-08-04 23:46:41,255 - WeChatAutoAdd - INFO - 🔄 列表检测未找到联系人，尝试改进方法...
2025-08-04 23:46:41,256 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:46:41,257 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:46:41,262 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:46:41,267 - WeChatAutoAdd - INFO - 💾 保存改进轮廓标记图像: screenshots/improved_contour_marked_20250804_234641.png
2025-08-04 23:46:41,268 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:46:41,269 - WeChatAutoAdd - INFO - 🔄 改进方法未找到联系人，回退到简单方法...
2025-08-04 23:46:41,269 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:46:41,270 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:46:41,271 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:46:41,280 - WeChatAutoAdd - INFO - 💾 保存轮廓标记图像: screenshots/contour_marked_20250804_234641.png
2025-08-04 23:46:41,282 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:46:41,283 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:46:41,283 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:46:41,284 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:46:41,284 - WeChatAutoAdd - INFO - ✅ 简单方法过滤后找到 0 个联系人
2025-08-04 23:46:41,287 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:46:41,287 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:46:41,288 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:46:41,288 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:46:41,290 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:46:41,290 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:46:41,302 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:46:41,302 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:46:41,809 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:46:41,809 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:46:41,811 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:46:41,811 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:46:41,812 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:46:41,812 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:46:42,813 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:46:42,813 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:46:42,814 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:46:42,814 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:46:42,817 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:46:42,817 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:46:42,829 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:46:42,829 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:46:42,829 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:46:42,829 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:46:42,830 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:46:42,830 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:46:42,831 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:46:42,831 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:46:42,891 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:46:42,891 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:46:42,902 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:46:42,902 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:46:42,902 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:46:42,902 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:46:42,913 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_234642.png
2025-08-04 23:46:42,913 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_234642.png
2025-08-04 23:46:42,914 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 23:46:42,914 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 23:46:42,916 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专门针对联系人列表）...
2025-08-04 23:46:42,916 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专门针对联系人列表）...
2025-08-04 23:46:42,916 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:46:42,916 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:46:42,917 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:46:42,917 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:46:42,922 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:46:42,922 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:46:42,934 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_234642.png
2025-08-04 23:46:42,934 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_234642.png
2025-08-04 23:46:42,935 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 0 个条目
2025-08-04 23:46:42,935 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 0 个条目
2025-08-04 23:46:42,935 - WeChatAutoAdd - INFO - 🔄 列表检测未找到联系人，尝试改进方法...
2025-08-04 23:46:42,935 - WeChatAutoAdd - INFO - 🔄 列表检测未找到联系人，尝试改进方法...
2025-08-04 23:46:42,936 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:46:42,936 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:46:42,937 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:46:42,937 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:46:42,938 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:46:42,938 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:46:42,948 - WeChatAutoAdd - INFO - 💾 保存改进轮廓标记图像: screenshots/improved_contour_marked_20250804_234642.png
2025-08-04 23:46:42,948 - WeChatAutoAdd - INFO - 💾 保存改进轮廓标记图像: screenshots/improved_contour_marked_20250804_234642.png
2025-08-04 23:46:42,949 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:46:42,949 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:46:42,950 - WeChatAutoAdd - INFO - 🔄 改进方法未找到联系人，回退到简单方法...
2025-08-04 23:46:42,950 - WeChatAutoAdd - INFO - 🔄 改进方法未找到联系人，回退到简单方法...
2025-08-04 23:46:42,950 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:46:42,950 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:46:42,951 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:46:42,951 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:46:42,953 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:46:42,953 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:46:42,962 - WeChatAutoAdd - INFO - 💾 保存轮廓标记图像: screenshots/contour_marked_20250804_234642.png
2025-08-04 23:46:42,962 - WeChatAutoAdd - INFO - 💾 保存轮廓标记图像: screenshots/contour_marked_20250804_234642.png
2025-08-04 23:46:42,963 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:46:42,963 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:46:42,964 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:46:42,964 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:46:42,964 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:46:42,964 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:46:42,965 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:46:42,965 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:46:42,965 - WeChatAutoAdd - INFO - ✅ 简单方法过滤后找到 0 个联系人
2025-08-04 23:46:42,965 - WeChatAutoAdd - INFO - ✅ 简单方法过滤后找到 0 个联系人
2025-08-04 23:46:42,966 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:46:42,966 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:46:42,966 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:46:42,966 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:46:42,968 - WeChatAutoAdd - INFO - 🔍 联系人识别结果不足，尝试OCR补充...
2025-08-04 23:46:42,968 - WeChatAutoAdd - INFO - 🔍 联系人识别结果不足，尝试OCR补充...
2025-08-04 23:46:42,969 - WeChatAutoAdd - INFO - 🔍 开始快速OCR识别...
2025-08-04 23:46:42,969 - WeChatAutoAdd - INFO - 🔍 开始快速OCR识别...
2025-08-04 23:46:42,970 - WeChatAutoAdd - INFO - 📐 OCR识别区域: (35, 91) 尺寸: 426x428
2025-08-04 23:46:42,970 - WeChatAutoAdd - INFO - 📐 OCR识别区域: (35, 91) 尺寸: 426x428
2025-08-04 23:48:20,669 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:48:20,670 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:48:20,670 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:48:20,723 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:48:21,331 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:48:21,332 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:48:21,332 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:48:22,332 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:48:22,333 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:48:22,337 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:48:22,345 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:48:22,346 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:48:22,346 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:48:22,347 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:48:22,413 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:48:22,425 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:48:22,426 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:48:22,437 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_234822.png
2025-08-04 23:48:22,440 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:48:22,440 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:48:22,448 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:48:22,449 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:48:22,449 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:48:22,450 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,450 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,450 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,454 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,454 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:48:22,456 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:48:22,457 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:48:22,457 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,458 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:48:22,460 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:48:22,461 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,462 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,462 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:48:22,462 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:48:22,463 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:48:22,463 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:48:22,463 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:48:22,472 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_234822.png
2025-08-04 23:48:22,473 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:48:22,474 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:48:22,475 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,475 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,475 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,475 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,476 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:48:22,476 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:48:22,477 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 194) 尺寸58x14 置信度70
2025-08-04 23:48:22,478 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:48:22,479 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,479 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 158) 尺寸59x14 置信度70
2025-08-04 23:48:22,480 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:48:22,480 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:48:22,481 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,481 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 122) 尺寸59x14 置信度70
2025-08-04 23:48:22,481 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,481 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:48:22,482 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 86) 尺寸72x14 置信度80
2025-08-04 23:48:22,482 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:48:22,482 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:48:22,483 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(110, 48) 尺寸99x17 置信度80
2025-08-04 23:48:22,483 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:48:22,483 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:48:22,483 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 5 个条目
2025-08-04 23:48:22,494 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 23:48:22,495 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专门针对联系人列表）...
2025-08-04 23:48:22,495 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:48:22,495 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:48:22,500 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:48:22,500 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:48:22,504 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:48:22,506 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,506 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,507 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,507 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,507 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:48:22,508 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:48:22,511 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:48:22,512 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,512 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:48:22,514 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:48:22,514 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,515 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,515 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:48:22,516 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:48:22,517 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:48:22,521 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:48:22,522 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:48:22,528 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_234822.png
2025-08-04 23:48:22,532 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:48:22,533 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:48:22,539 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,539 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,541 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,542 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:48:22,542 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:48:22,543 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:48:22,543 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 194) 尺寸58x14 置信度70
2025-08-04 23:48:22,543 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:48:22,544 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,544 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 158) 尺寸59x14 置信度70
2025-08-04 23:48:22,545 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:48:22,546 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:48:22,546 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:48:22,546 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 122) 尺寸59x14 置信度70
2025-08-04 23:48:22,547 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:48:22,547 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:48:22,547 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 86) 尺寸72x14 置信度80
2025-08-04 23:48:22,547 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:48:22,548 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:48:22,548 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(110, 48) 尺寸99x17 置信度80
2025-08-04 23:48:22,548 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:48:22,548 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:48:22,549 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 5 个条目
2025-08-04 23:48:22,549 - WeChatAutoAdd - INFO - ✅ 联系人列表检测成功找到 5 个条目
2025-08-04 23:48:22,550 - WeChatAutoAdd - INFO - ✅ 轮廓检测识别到 5 个联系人
2025-08-04 23:48:22,550 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:48:22,554 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:48:22,557 - WeChatAutoAdd - INFO - 🔍 联系人过滤结果: 原始5 -> 过滤5 -> 去重5 -> 最终5
2025-08-04 23:48:22,557 - WeChatAutoAdd - INFO - 🎯 最终识别到 5 个有效联系人
2025-08-04 23:48:22,567 - WeChatAutoAdd - INFO - 💾 联系人识别调试图像已保存: screenshots\contact_recognition_debug_20250804_234822.png
2025-08-04 23:51:28,742 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:51:28,743 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:51:28,743 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:51:30,593 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:51:31,129 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:51:31,130 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:51:31,130 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:51:32,131 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:51:32,131 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:51:32,134 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:51:32,142 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:51:32,142 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:51:32,143 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:51:32,144 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:51:32,221 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:51:32,233 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:51:32,234 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:51:32,249 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_235132.png
2025-08-04 23:51:32,252 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:51:32,253 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:51:32,265 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:51:32,266 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:51:32,267 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:51:32,268 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,268 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,269 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,269 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,270 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:51:32,271 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 58x14
2025-08-04 23:51:32,272 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:51:32,272 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,278 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:51:32,279 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:51:32,279 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,280 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,280 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 72x14
2025-08-04 23:51:32,281 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:51:32,281 - WeChatAutoAdd - DEBUG - ❌ 位置在分类标签区域: y=48, 阈值=184
2025-08-04 23:51:32,282 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 44x13
2025-08-04 23:51:32,284 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:51:32,293 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_235132.png
2025-08-04 23:51:32,294 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:51:32,295 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:51:32,295 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,296 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,296 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,296 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,297 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:51:32,299 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 58x14
2025-08-04 23:51:32,299 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:51:32,300 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,300 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:51:32,300 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:51:32,300 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,301 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,301 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 72x14
2025-08-04 23:51:32,301 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:51:32,302 - WeChatAutoAdd - DEBUG - ❌ 位置在分类标签区域: y=48, 阈值=184
2025-08-04 23:51:32,302 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 44x13
2025-08-04 23:51:32,302 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:51:32,302 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 0 个条目
2025-08-04 23:51:32,304 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 23:51:32,307 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专门针对联系人列表）...
2025-08-04 23:51:32,310 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:51:32,310 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:51:32,315 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:51:32,317 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:51:32,319 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:51:32,320 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,322 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,335 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,336 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,337 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:51:32,337 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 58x14
2025-08-04 23:51:32,338 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:51:32,338 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,339 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:51:32,345 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:51:32,347 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,349 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,350 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 72x14
2025-08-04 23:51:32,352 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:51:32,353 - WeChatAutoAdd - DEBUG - ❌ 位置在分类标签区域: y=48, 阈值=184
2025-08-04 23:51:32,353 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 44x13
2025-08-04 23:51:32,353 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:51:32,362 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_235132.png
2025-08-04 23:51:32,362 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:51:32,364 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:51:32,365 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,366 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,366 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,367 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:51:32,367 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:51:32,367 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 58x14
2025-08-04 23:51:32,368 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:51:32,368 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,368 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:51:32,368 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:51:32,369 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 59x14
2025-08-04 23:51:32,370 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:51:32,370 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 72x14
2025-08-04 23:51:32,371 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:51:32,371 - WeChatAutoAdd - DEBUG - ❌ 位置在分类标签区域: y=48, 阈值=184
2025-08-04 23:51:32,371 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 44x13
2025-08-04 23:51:32,372 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:51:32,372 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 0 个条目
2025-08-04 23:51:32,376 - WeChatAutoAdd - INFO - 🔄 列表检测未找到联系人，尝试改进方法...
2025-08-04 23:51:32,378 - WeChatAutoAdd - INFO - 🔧 开始改进轮廓检测（基于成功的简单方法）...
2025-08-04 23:51:32,378 - WeChatAutoAdd - INFO - 📐 改进识别区域: (35, 61) 尺寸: 496x367
2025-08-04 23:51:32,379 - WeChatAutoAdd - INFO - 🔍 找到 1 个轮廓
2025-08-04 23:51:32,385 - WeChatAutoAdd - INFO - 💾 保存改进轮廓标记图像: screenshots/improved_contour_marked_20250804_235132.png
2025-08-04 23:51:32,385 - WeChatAutoAdd - INFO - ✅ 改进轮廓检测找到 0 个联系人
2025-08-04 23:51:32,386 - WeChatAutoAdd - INFO - 🔄 改进方法未找到联系人，回退到简单方法...
2025-08-04 23:51:32,387 - WeChatAutoAdd - INFO - 🔧 开始最简单轮廓检测...
2025-08-04 23:51:32,387 - WeChatAutoAdd - INFO - 📐 简单识别区域: 整个宽度 x 上半部分高度 (710x306)
2025-08-04 23:51:32,388 - WeChatAutoAdd - INFO - 🔍 找到 12 个轮廓
2025-08-04 23:51:32,399 - WeChatAutoAdd - INFO - 💾 保存轮廓标记图像: screenshots/contour_marked_20250804_235132.png
2025-08-04 23:51:32,400 - WeChatAutoAdd - INFO - 🔍 简单轮廓1: 位置(6, 22) 尺寸12x3
2025-08-04 23:51:32,400 - WeChatAutoAdd - INFO - 🔍 简单轮廓2: 位置(19, 0) 尺寸7x7
2025-08-04 23:51:32,400 - WeChatAutoAdd - INFO - 🔍 简单轮廓3: 位置(0, 0) 尺寸710x306
2025-08-04 23:51:32,401 - WeChatAutoAdd - INFO - ✅ 简单轮廓检测找到 3 个候选项
2025-08-04 23:51:32,401 - WeChatAutoAdd - INFO - ✅ 简单方法过滤后找到 0 个联系人
2025-08-04 23:51:32,401 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:51:32,402 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:51:32,404 - WeChatAutoAdd - INFO - 🔍 联系人识别结果不足，尝试OCR补充...
2025-08-04 23:51:32,405 - WeChatAutoAdd - INFO - 🔍 开始快速OCR识别...
2025-08-04 23:51:32,405 - WeChatAutoAdd - INFO - 📐 OCR识别区域: (35, 91) 尺寸: 426x428
2025-08-04 23:55:07,464 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 23:55:07,464 - WeChatAutoAdd - INFO - 🔍 开始优化的微信窗口截图流程...
2025-08-04 23:55:07,465 - WeChatAutoAdd - INFO - 🔄 使用window_manager激活微信窗口...
2025-08-04 23:55:07,510 - WeChatAutoAdd - INFO - 🎯 选择微信窗口: 微信 (句柄: 12062182)
2025-08-04 23:55:08,023 - WeChatAutoAdd - INFO - ✅ 微信窗口激活成功
2025-08-04 23:55:08,023 - WeChatAutoAdd - INFO - ✅ 微信窗口已在前台
2025-08-04 23:55:08,023 - WeChatAutoAdd - INFO - ⏳ 等待微信窗口稳定...
2025-08-04 23:55:09,024 - WeChatAutoAdd - INFO - ✅ 微信窗口保持稳定状态
2025-08-04 23:55:09,025 - WeChatAutoAdd - INFO - 📸 开始精确客户端区域截图...
2025-08-04 23:55:09,028 - WeChatAutoAdd - INFO - 📱 微信窗口信息: 位置(0, 0) 尺寸726x650
2025-08-04 23:55:09,034 - WeChatAutoAdd - INFO - 📐 客户端区域: (0, 0) 尺寸: 1x1
2025-08-04 23:55:09,035 - WeChatAutoAdd - WARNING - ⚠️ 客户端区域尺寸异常: 1x1
2025-08-04 23:55:09,035 - WeChatAutoAdd - INFO - 🔄 使用备选截图方案...
2025-08-04 23:55:09,036 - WeChatAutoAdd - INFO - 📐 备选截图区域: (8, 30) 尺寸: 710x612
2025-08-04 23:55:09,099 - WeChatAutoAdd - INFO - ✅ 备选截图完成
2025-08-04 23:55:09,107 - WeChatAutoAdd - INFO - ✅ 截图质量验证通过: 尺寸710x612, 清晰度885.63, 标准差15.10
2025-08-04 23:55:09,108 - WeChatAutoAdd - INFO - ✅ 成功获取高质量微信窗口截图
2025-08-04 23:55:09,116 - WeChatAutoAdd - DEBUG - 💾 保存调试截图: screenshots\optimized_wechat_window_20250804_235509.png
2025-08-04 23:55:09,118 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:55:09,118 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:55:09,124 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:55:09,125 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:55:09,125 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:55:09,126 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,126 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,127 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,127 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,127 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:55:09,128 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:55:09,128 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:55:09,129 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,129 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:55:09,129 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:55:09,130 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,130 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,131 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:55:09,131 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:55:09,131 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:55:09,132 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:55:09,132 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:55:09,139 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_235509.png
2025-08-04 23:55:09,139 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:55:09,139 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:55:09,140 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,140 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,140 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,141 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,141 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:55:09,141 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:55:09,142 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 194) 尺寸58x14 置信度70
2025-08-04 23:55:09,142 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:55:09,143 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,143 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 158) 尺寸59x14 置信度70
2025-08-04 23:55:09,144 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:55:09,144 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:55:09,144 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,145 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 122) 尺寸59x14 置信度70
2025-08-04 23:55:09,145 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,146 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:55:09,146 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 86) 尺寸72x14 置信度80
2025-08-04 23:55:09,146 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:55:09,147 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:55:09,147 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(110, 48) 尺寸99x17 置信度80
2025-08-04 23:55:09,147 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:55:09,148 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:55:09,148 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 5 个条目
2025-08-04 23:55:09,152 - WeChatAutoAdd - INFO - 🔍 开始优化的联系人识别流程...
2025-08-04 23:55:09,152 - WeChatAutoAdd - INFO - 🔍 开始优化轮廓检测（专门针对联系人列表）...
2025-08-04 23:55:09,153 - WeChatAutoAdd - INFO - 📋 开始联系人列表条目检测...
2025-08-04 23:55:09,153 - WeChatAutoAdd - INFO - 📐 联系人列表识别区域: (0, 0) 尺寸: 284x612
2025-08-04 23:55:09,158 - WeChatAutoAdd - INFO - 🔍 找到 19 个轮廓
2025-08-04 23:55:09,159 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:55:09,160 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:55:09,160 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,160 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,161 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,161 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,161 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:55:09,162 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:55:09,162 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:55:09,163 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,163 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:55:09,164 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:55:09,164 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,165 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,165 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:55:09,166 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:55:09,166 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:55:09,166 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:55:09,167 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:55:09,173 - WeChatAutoAdd - INFO - 💾 保存联系人列表标记图像: screenshots/list_items_marked_20250804_235509.png
2025-08-04 23:55:09,173 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 19x14
2025-08-04 23:55:09,174 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 16x22
2025-08-04 23:55:09,174 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,175 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,175 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,175 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x22
2025-08-04 23:55:09,176 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x11
2025-08-04 23:55:09,176 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 58x14 比例4.14 面积812 宽度占比0.20
2025-08-04 23:55:09,176 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 194) 尺寸58x14 置信度70
2025-08-04 23:55:09,177 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 7x11
2025-08-04 23:55:09,177 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,177 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 158) 尺寸59x14 置信度70
2025-08-04 23:55:09,178 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 20x22
2025-08-04 23:55:09,178 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 6x11
2025-08-04 23:55:09,178 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 59x14 比例4.21 面积826 宽度占比0.21
2025-08-04 23:55:09,179 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 122) 尺寸59x14 置信度70
2025-08-04 23:55:09,179 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x18
2025-08-04 23:55:09,180 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 72x14 比例5.14 面积1008 宽度占比0.25
2025-08-04 23:55:09,180 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(74, 86) 尺寸72x14 置信度80
2025-08-04 23:55:09,180 - WeChatAutoAdd - DEBUG - ❌ 尺寸不符: 22x19
2025-08-04 23:55:09,181 - WeChatAutoAdd - DEBUG - ✅ 通过过滤: 99x17 比例5.82 面积1683 宽度占比0.35
2025-08-04 23:55:09,181 - WeChatAutoAdd - DEBUG - 📋 联系人条目: 位置(110, 48) 尺寸99x17 置信度80
2025-08-04 23:55:09,181 - WeChatAutoAdd - DEBUG - ❌ 宽度占比不足: 0.15
2025-08-04 23:55:09,182 - WeChatAutoAdd - DEBUG - ❌ 宽高比不符: 1.35
2025-08-04 23:55:09,182 - WeChatAutoAdd - INFO - ✅ 联系人列表检测找到 5 个条目
2025-08-04 23:55:09,182 - WeChatAutoAdd - INFO - ✅ 联系人列表检测成功找到 5 个条目
2025-08-04 23:55:09,183 - WeChatAutoAdd - INFO - ✅ 轮廓检测识别到 5 个联系人
2025-08-04 23:55:09,183 - WeChatAutoAdd - INFO - 🔍 开始优化模板匹配...
2025-08-04 23:55:09,183 - WeChatAutoAdd - INFO - 📐 模板匹配区域: (35, 91) 尺寸: 426x428
2025-08-04 23:55:09,185 - WeChatAutoAdd - INFO - 🔍 联系人过滤结果: 原始5 -> 过滤5 -> 去重5 -> 最终5
2025-08-04 23:55:09,186 - WeChatAutoAdd - INFO - 🎯 最终识别到 5 个有效联系人
2025-08-04 23:55:09,197 - WeChatAutoAdd - INFO - 💾 联系人识别调试图像已保存: screenshots\contact_recognition_debug_20250804_235509.png
