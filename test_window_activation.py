#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信窗口激活功能
验证wechat_contact_detector.py是否正确调用window_manager.py
"""

import logging
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_window_activation():
    """测试窗口激活功能"""
    print("🚀 测试微信窗口激活功能")
    print("=" * 50)
    
    try:
        # 导入合并后的检测器
        from wechat_contact_detector import WeChatContactDetector
        
        print("✅ 成功导入 WeChatContactDetector")
        
        # 创建检测器实例
        detector = WeChatContactDetector(debug_mode=True)
        print("✅ 成功创建检测器实例")
        
        # 测试窗口激活
        print("\n🔍 测试微信窗口激活...")
        success = detector._ensure_wechat_window_active()
        
        if success:
            print("✅ 微信窗口激活成功！")
            print("🎯 window_manager.py 集成正常工作")
        else:
            print("❌ 微信窗口激活失败")
            print("⚠️ 可能的原因：")
            print("  - 微信未启动")
            print("  - window_manager.py 导入失败")
            print("  - 窗口权限问题")
        
        return success
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_window_manager_direct():
    """直接测试window_manager模块"""
    print("\n🔧 直接测试 window_manager 模块")
    print("=" * 50)
    
    try:
        from window_manager import WeChatWindowManager
        print("✅ 成功导入 WeChatWindowManager")
        
        # 创建窗口管理器
        manager = WeChatWindowManager()
        print("✅ 成功创建窗口管理器实例")
        
        # 查找微信窗口
        windows = manager.find_all_wechat_windows()
        print(f"🔍 找到 {len(windows)} 个微信窗口")
        
        if windows:
            for i, window in enumerate(windows):
                title = window.get('title', '未知')
                hwnd = window.get('hwnd', 0)
                is_main = window.get('is_main', False)
                print(f"  {i+1}. {title} (句柄: {hwnd}) {'[主窗口]' if is_main else ''}")
            
            # 尝试激活第一个窗口
            first_window = windows[0]
            hwnd = first_window.get('hwnd')
            if hwnd:
                print(f"\n🎯 尝试激活窗口: {first_window.get('title', '未知')}")
                success = manager.activate_window(hwnd)
                if success:
                    print("✅ 窗口激活成功！")
                else:
                    print("❌ 窗口激活失败")
                return success
        else:
            print("⚠️ 未找到微信窗口")
            return False
            
    except ImportError as e:
        print(f"❌ window_manager 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ window_manager 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 微信窗口激活功能测试")
    print("🎯 验证 wechat_contact_detector.py 是否正确集成 window_manager.py")
    print("=" * 70)
    
    # 测试1：直接测试window_manager
    result1 = test_window_manager_direct()
    
    # 测试2：通过合并后的检测器测试
    result2 = test_window_activation()
    
    print("\n" + "=" * 70)
    print("📊 测试结果汇总")
    print("=" * 70)
    print(f"  window_manager 直接测试: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"  检测器集成测试: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print("\n🎉 所有测试通过！window_manager.py 集成成功")
        print("✅ wechat_contact_detector.py 现在可以正确激活微信窗口")
    elif result1 and not result2:
        print("\n⚠️ window_manager 工作正常，但检测器集成有问题")
        print("🔧 需要检查 wechat_contact_detector.py 中的集成代码")
    elif not result1:
        print("\n❌ window_manager 本身有问题")
        print("🔧 需要检查 window_manager.py 或微信环境")
    
    print("\n💡 提示:")
    print("  - 确保微信已启动并可见")
    print("  - 确保有足够的窗口操作权限")
    print("  - 检查 window_manager.py 是否在同一目录")

if __name__ == "__main__":
    main()
