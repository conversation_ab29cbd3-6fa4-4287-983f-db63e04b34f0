#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清理后的联系人检测功能
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

def test_cleaned_detection():
    """测试清理后的联系人检测功能"""
    try:
        from friend import WeChatImageRecognition
        
        print("🧹 清理后的联系人检测测试")
        print("=" * 50)
        print("🎯 只标记绿色粗框：选中的最后一个条目（最下方）")
        
        # 创建识别实例
        recognizer = WeChatImageRecognition(debug_mode=True)
        
        # 获取截图
        print("\n📸 获取截图...")
        screenshot = recognizer.capture_wechat_window()
        
        if screenshot is None:
            print("❌ 无法获取截图")
            return False
        
        height, width = screenshot.shape[:2]
        print(f"✅ 截图成功: {width}x{height}")
        
        # 测试联系人列表检测（只返回最后一个）
        print("\n🎯 执行联系人列表检测（只返回最后一个）...")
        contacts = recognizer._find_contacts_by_list_items(screenshot)
        print(f"📊 检测结果: {len(contacts)} 个联系人条目")
        
        if contacts:
            print(f"\n📋 检测到的联系人条目:")
            for i, contact in enumerate(contacts):
                name = contact.get('name', f'联系人{i+1}')
                coord = contact.get('coord', (0, 0))
                confidence = contact.get('confidence', 0)
                source = contact.get('source', 'unknown')
                bbox = contact.get('bbox', (0, 0, 0, 0))
                
                print(f"  {i+1}. {name}")
                print(f"     坐标: {coord}")
                print(f"     置信度: {confidence}%")
                print(f"     来源: {source}")
                print(f"     边界框: {bbox}")
                
                if 'last' in source:
                    print(f"     ✅ 确认为最后一个条目")
        else:
            print("⚠️ 未检测到联系人条目")
        
        # 测试完整识别流程
        print(f"\n🎯 测试完整识别流程...")
        all_contacts = recognizer.find_contacts_in_screenshot(screenshot)
        print(f"📊 完整识别结果: {len(all_contacts)} 个联系人")
        
        if all_contacts:
            print(f"\n📋 完整识别详情:")
            for i, contact in enumerate(all_contacts):
                name = contact.get('name', f'联系人{i+1}')
                coord = contact.get('coord', (0, 0))
                confidence = contact.get('confidence', 0)
                source = contact.get('source', 'unknown')
                
                print(f"  {i+1}. {name}")
                print(f"     坐标: {coord}")
                print(f"     置信度: {confidence}%")
                print(f"     来源: {source}")
        
        return len(contacts) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 清理后的联系人检测测试")
    print("🎯 目标: 验证清理后的功能")
    print("🧹 特点: 删除无用方法，只标记绿色粗框")
    print("=" * 60)
    
    result = test_cleaned_detection()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    print(f"  清理后检测: {'✅ 通过' if result else '❌ 失败'}")
    
    if result:
        print("🎉 清理后的功能正常工作！")
        print("\n💡 清理成果:")
        print("  🧹 删除了简单轮廓检测方法")
        print("  🧹 删除了改进轮廓检测方法")
        print("  🧹 删除了调试轮廓检测方法")
        print("  🧹 简化了主要轮廓检测逻辑")
        print("  🎨 只标记绿色粗框（选中的最后一个条目）")
        print("  📊 只返回最下方的联系人条目")
    else:
        print("❌ 清理后功能有问题")
        print("\n🔍 可能需要:")
        print("  - 检查代码清理是否正确")
        print("  - 验证核心功能是否保留")
        print("  - 分析调试图像")
    
    print("\n📁 查看调试图像:")
    print("  📸 selected_contact_*.png - 只标记选中的联系人")
    print("  🎨 只显示绿色粗框标记最后一个条目")
    
    print("\n💡 功能说明:")
    print("  - 现在只保留核心的联系人列表检测方法")
    print("  - 只返回最下方的联系人条目（通常是'联系人'分类）")
    print("  - 调试图像只显示选中的条目，不显示其他轮廓")

if __name__ == "__main__":
    main()
