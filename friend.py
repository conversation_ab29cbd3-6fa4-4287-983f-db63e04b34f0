#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信图像识别模块 - 联系人识别与点击

作者: AI Assistant
创建时间: 2025-07-24
更新时间: 2025-08-04
版本: 2.0.0

功能特性:
- 自动检测微信窗口（主窗口、添加朋友窗口）
- 图像识别联系人列表和联系人条目
- OCR文字识别联系人姓名
- 智能点击执行联系人选择
- 模板匹配算法识别可点击区域
- 完整的错误处理机制
- 详细的调试日志和中间图像保存
"""

import time
import logging
import cv2
import numpy as np
import pyautogui
import pygetwindow as gw
from typing import Optional, Tuple, List, Dict
import os
from datetime import datetime
import re

# OCR相关导入
try:
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logging.warning("⚠️ pytesseract未安装，OCR功能不可用")

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    logging.warning("⚠️ easyocr未安装，EasyOCR功能不可用")

# 配置pyautogui
pyautogui.FAILSAFE = True  # 鼠标移到屏幕左上角时停止
pyautogui.PAUSE = 0.5      # 每次操作间隔0.5秒

class WeChatImageRecognition:
    """微信图像识别类 - 联系人识别与点击"""
    
    def __init__(self, debug_mode: bool = True):
        """
        初始化微信自动添加朋友实例
        
        Args:
            debug_mode: 是否开启调试模式
        """
        self.debug_mode = debug_mode
        self.setup_logging()
        self.screenshot_dir = "screenshots"
        self.ensure_screenshot_dir()
        
        # 微信窗口相关配置
        self.wechat_window_titles = [
            "微信",
            "WeChat",
            "添加朋友",
            "Add Friends",
            "添加联系人",
            "Add Contact"
        ]
        
        # 按钮识别相关配置
        self.button_texts = [
            "联系人",
            "Add to Contacts",
            "联系",
            "Add"
        ]
        
        self.logger.info("微信自动添加朋友脚本初始化完成")
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = logging.DEBUG if self.debug_mode else logging.INFO
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 设置控制台日志
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # 设置文件日志
        file_handler = logging.FileHandler(
            f'wechat_auto_add_{datetime.now().strftime("%Y%m%d")}.log',
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        # 配置logger
        self.logger = logging.getLogger('WeChatAutoAdd')
        self.logger.setLevel(log_level)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def ensure_screenshot_dir(self):
        """确保截图目录存在"""
        if not os.path.exists(self.screenshot_dir):
            os.makedirs(self.screenshot_dir)
            self.logger.info(f"创建截图目录: {self.screenshot_dir}")
    
    def find_wechat_windows(self) -> List[gw.Win32Window]:
        """
        查找所有微信相关窗口

        Returns:
            微信窗口列表
        """
        wechat_windows = []
        all_windows = gw.getAllWindows()

        for window in all_windows:
            if window.title and any(title in window.title for title in self.wechat_window_titles):
                # 兼容不同版本的pygetwindow库
                is_visible = self._check_window_visible(window)
                if is_visible and window.width > 100 and window.height > 100:
                    wechat_windows.append(window)
                    self.logger.debug(f"找到微信窗口: {window.title} - {window.width}x{window.height}")

        self.logger.info(f"共找到 {len(wechat_windows)} 个微信窗口")
        return wechat_windows

    def _check_window_visible(self, window) -> bool:
        """
        检查窗口是否可见（兼容不同版本的pygetwindow）

        Args:
            window: 窗口对象

        Returns:
            窗口是否可见
        """
        try:
            # 尝试使用 isVisible 属性
            if hasattr(window, 'isVisible'):
                return window.isVisible
            # 尝试使用 visible 属性
            elif hasattr(window, 'visible'):
                return window.visible
            # 如果都没有，假设窗口可见
            else:
                self.logger.warning(f"无法检查窗口可见性，假设窗口可见: {window.title}")
                return True
        except Exception as e:
            self.logger.warning(f"检查窗口可见性时发生错误: {e}")
            return True
    
    def find_add_friend_window(self) -> Optional[gw.Win32Window]:
        """
        查找"添加朋友"窗口
        
        Returns:
            添加朋友窗口对象，如果未找到返回None
        """
        wechat_windows = self.find_wechat_windows()
        
        # 优先查找包含"添加朋友"的窗口
        for window in wechat_windows:
            if "添加朋友" in window.title or "Add Friends" in window.title:
                self.logger.info(f"找到添加朋友窗口: {window.title}")
                return window
        
        # 如果没有找到专门的添加朋友窗口，查找主微信窗口
        for window in wechat_windows:
            if window.title == "微信" or window.title == "WeChat":
                self.logger.info(f"找到微信主窗口: {window.title}")
                return window
        
        self.logger.warning("未找到微信添加朋友窗口")
        return None

    def clean_screenshots_directory(self):
        """
        清理screenshots目录下的所有图片文件
        """
        try:
            if not os.path.exists(self.screenshot_dir):
                os.makedirs(self.screenshot_dir)
                self.logger.info(f"创建screenshots目录: {self.screenshot_dir}")
                return

            # 获取所有图片文件
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
            files_to_remove = []

            for file in os.listdir(self.screenshot_dir):
                file_path = os.path.join(self.screenshot_dir, file)
                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file.lower())
                    if ext in image_extensions:
                        files_to_remove.append(file_path)

            # 删除图片文件
            removed_count = 0
            for file_path in files_to_remove:
                try:
                    os.remove(file_path)
                    removed_count += 1
                except Exception as e:
                    self.logger.warning(f"删除文件失败 {file_path}: {e}")

            if removed_count > 0:
                self.logger.info(f"清理screenshots目录: 删除了 {removed_count} 个图片文件")
            else:
                self.logger.info("screenshots目录已经是干净的")

        except Exception as e:
            self.logger.error(f"清理screenshots目录失败: {e}")

    def verify_add_friend_window(self, screenshot: np.ndarray) -> bool:
        """
        验证截图是否为添加朋友窗口

        Args:
            screenshot: 窗口截图

        Returns:
            是否为添加朋友窗口
        """
        try:
            height, width = screenshot.shape[:2]

            # 检查截图尺寸是否合理
            if width < 200 or height < 300:
                self.logger.warning(f"窗口尺寸过小: {width}x{height}")
                return False

            if width > 2000 or height > 2000:
                self.logger.warning(f"窗口尺寸过大: {width}x{height}")
                return False

            # 检查是否有足够的内容
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # 计算非空白区域
            non_white_pixels = np.sum(gray < 240)
            total_pixels = width * height
            content_ratio = non_white_pixels / total_pixels

            if content_ratio < 0.1:
                self.logger.warning(f"窗口内容过少: {content_ratio:.3f}")
                return False

            # 检查是否有边缘（表示有UI元素）
            edges = cv2.Canny(gray, 50, 150)
            edge_pixels = np.sum(edges > 0)
            edge_ratio = edge_pixels / total_pixels

            if edge_ratio < 0.01:
                self.logger.warning(f"窗口边缘过少: {edge_ratio:.3f}")
                return False

            self.logger.info(f"窗口验证通过: 尺寸{width}x{height}, 内容比例{content_ratio:.3f}, 边缘比例{edge_ratio:.3f}")
            return True

        except Exception as e:
            self.logger.error(f"验证窗口失败: {e}")
            return False

    def verify_screenshot_content(self, screenshot: np.ndarray) -> bool:
        """
        验证截图内容是否合理

        Args:
            screenshot: 截图数组

        Returns:
            截图内容是否合理
        """
        try:
            height, width = screenshot.shape[:2]

            # 检查是否为空白或纯色图像
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # 计算图像的标准差，纯色图像标准差很小
            _, std_dev = cv2.meanStdDev(gray)
            std_dev = float(std_dev[0][0])
            if std_dev < 5:
                self.logger.warning(f"截图可能为纯色图像，标准差: {std_dev}")
                return False

            # 检查是否有足够的变化
            edges = cv2.Canny(gray, 50, 150)
            edge_count = np.sum(edges > 0)
            edge_ratio = edge_count / (width * height)

            if edge_ratio < 0.005:
                self.logger.warning(f"截图边缘过少，可能为空白图像: {edge_ratio}")
                return False

            # 检查颜色分布
            mean_color = np.mean(screenshot, axis=(0, 1))
            if np.all(mean_color > 240):  # 接近白色
                self.logger.warning("截图可能为白色背景")
                return False

            self.logger.debug(f"截图内容验证通过: 标准差{std_dev:.2f}, 边缘比例{edge_ratio:.4f}")
            return True

        except Exception as e:
            self.logger.error(f"验证截图内容失败: {e}")
            return False
    
    def capture_window_screenshot(self, window: gw.Win32Window) -> Optional[np.ndarray]:
        """
        截取指定窗口的截图（改进版，确保截取正确的添加朋友窗口）

        Args:
            window: 目标窗口

        Returns:
            截图的numpy数组，失败返回None
        """
        try:
            # 首先验证窗口是否为添加朋友窗口
            if not ("添加朋友" in window.title or "Add Friends" in window.title or "微信" in window.title):
                self.logger.warning(f"窗口标题不匹配: {window.title}")

            # 方法1: 尝试使用pygetwindow的方式
            try:
                # 激活并置顶窗口
                window.activate()
                time.sleep(0.5)  # 增加等待时间确保窗口激活

                # 获取窗口位置和大小
                left, top, width, height = window.left, window.top, window.width, window.height
                self.logger.info(f"目标窗口: {window.title}")
                self.logger.info(f"窗口位置: ({left}, {top}), 尺寸: {width}x{height}")

                # 验证窗口尺寸
                if width <= 0 or height <= 0:
                    raise ValueError(f"窗口尺寸无效: {width}x{height}")

                if width < 200 or height < 300:
                    self.logger.warning(f"窗口尺寸过小，可能不是添加朋友窗口: {width}x{height}")

                # 截取窗口区域
                screenshot = pyautogui.screenshot(region=(int(left), int(top), int(width), int(height)))

                # 转换为numpy数组
                screenshot_np = np.array(screenshot)
                screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                # 验证截图内容
                if not self.verify_screenshot_content(screenshot_bgr):
                    self.logger.warning("截图内容验证失败，可能截取了错误的窗口")

                # 保存调试截图
                if self.debug_mode:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_path = os.path.join(self.screenshot_dir, f"window_capture_method1_{timestamp}.png")
                    cv2.imwrite(debug_path, screenshot_bgr)
                    self.logger.debug(f"保存方法1截图: {debug_path}")

                return screenshot_bgr

            except Exception as e1:
                self.logger.warning(f"方法1截图失败: {e1}, 尝试方法2")

                # 方法2: 使用win32gui直接获取窗口句柄
                try:
                    import win32gui
                    import win32ui
                    import win32con
                    from PIL import Image
                except ImportError:
                    raise Exception("win32gui模块未安装，无法使用方法2")

                # 通过窗口标题重新查找窗口句柄，优先查找添加朋友窗口
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        # 优先匹配添加朋友窗口
                        if "添加朋友" in window_title or "Add Friends" in window_title:
                            windows.insert(0, (hwnd, window_title))  # 插入到开头，优先级最高
                        elif window.title in window_title or window_title in window.title:
                            windows.append((hwnd, window_title))
                    return True

                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)

                if not windows:
                    raise Exception("无法找到窗口句柄")

                # 选择最匹配的窗口
                hwnd, matched_title = windows[0]
                self.logger.info(f"使用win32方法截取窗口: {matched_title}")

                # 获取窗口DC
                hwndDC = win32gui.GetWindowDC(hwnd)
                mfcDC = win32ui.CreateDCFromHandle(hwndDC)
                saveDC = mfcDC.CreateCompatibleDC()

                # 获取窗口尺寸
                left, top, right, bottom = win32gui.GetWindowRect(hwnd)
                width = right - left
                height = bottom - top

                self.logger.info(f"win32窗口尺寸: {width}x{height}, 位置: ({left}, {top})")

                # 验证窗口尺寸
                if width <= 0 or height <= 0:
                    raise Exception(f"win32窗口尺寸无效: {width}x{height}")

                # 创建bitmap
                saveBitMap = win32ui.CreateBitmap()
                saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
                saveDC.SelectObject(saveBitMap)

                # 复制窗口内容
                result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
                if not result:
                    self.logger.warning("win32 BitBlt操作可能失败")

                # 转换为PIL图像
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                im = Image.frombuffer('RGB', (bmpinfo['bmWidth'], bmpinfo['bmHeight']), bmpstr, 'raw', 'BGRX', 0, 1)

                # 清理资源
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)

                # 转换为numpy数组
                screenshot_np = np.array(im)
                screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                # 验证截图内容
                if not self.verify_screenshot_content(screenshot_bgr):
                    self.logger.warning("win32截图内容验证失败")

                # 保存调试截图
                if self.debug_mode:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_path = os.path.join(self.screenshot_dir, f"window_capture_win32_{timestamp}.png")
                    cv2.imwrite(debug_path, screenshot_bgr)
                    self.logger.info(f"保存win32截图: {debug_path}")

                self.logger.info("win32截图成功")
                return screenshot_bgr

        except Exception as e:
            self.logger.error(f"截取窗口截图失败: {e}")

            # 方法3: 备选方案 - 截取整个屏幕
            try:
                self.logger.info("尝试备选方案：截取整个屏幕")
                screenshot = pyautogui.screenshot()
                screenshot_np = np.array(screenshot)
                screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                if self.debug_mode:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    debug_path = os.path.join(self.screenshot_dir, f"fullscreen_capture_{timestamp}.png")
                    cv2.imwrite(debug_path, screenshot_bgr)
                    self.logger.debug(f"保存全屏截图: {debug_path}")

                return screenshot_bgr

            except Exception as e3:
                self.logger.error(f"备选截图方案也失败: {e3}")
                return None
            
    def _is_green_button(self, roi: np.ndarray) -> bool:
        """
        检查ROI区域是否为绿色按钮

        Args:
            roi: 待检查的图像区域

        Returns:
            是否为绿色按钮
        """
        if roi.size == 0:
            return False

        try:
            # 转换为HSV颜色空间以便更好地检测绿色
            if len(roi.shape) == 3:
                hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)
            else:
                # 如果是灰度图，转换为BGR再转HSV
                bgr = cv2.cvtColor(roi, cv2.COLOR_GRAY2BGR)
                hsv = cv2.cvtColor(bgr, cv2.COLOR_BGR2HSV)

            # 定义绿色的HSV范围（微信绿色）
            # 微信的绿色大约是 HSV(120, 100, 80) 左右
            lower_green = np.array([40, 40, 40])   # 较宽的绿色范围下限
            upper_green = np.array([80, 255, 255]) # 较宽的绿色范围上限

            # 创建绿色掩码
            green_mask = cv2.inRange(hsv, lower_green, upper_green)

            # 计算绿色像素的比例
            green_pixels = np.sum(green_mask > 0)
            total_pixels = roi.shape[0] * roi.shape[1]
            green_ratio = green_pixels / total_pixels

            # 如果绿色像素超过30%，认为是绿色按钮
            is_green = green_ratio > 0.3

            if is_green:
                self.logger.debug(f"检测到绿色按钮: 绿色像素比例 {green_ratio:.2f}")

            return is_green

        except Exception as e:
            self.logger.debug(f"绿色按钮检测失败: {e}")
            # 备选方案：检查平均颜色是否偏绿
            try:
                if len(roi.shape) == 3:
                    # BGR格式，绿色通道是第二个
                    mean_color = np.mean(roi, axis=(0, 1))
                    # 如果绿色通道明显高于其他通道，可能是绿色按钮
                    if len(mean_color) >= 3 and mean_color[1] > mean_color[0] + 20 and mean_color[1] > mean_color[2] + 20:
                        self.logger.debug(f"备选方案检测到绿色按钮: BGR均值 {mean_color}")
                        return True
            except:
                pass
            return False







    def find_button_by_image_processing(self, screenshot: np.ndarray) -> Optional[Tuple[int, int, bool]]:
        """
        使用改进的图像处理技术查找"添加到通讯录"按钮

        Args:
            screenshot: 窗口截图

        Returns:
            (按钮中心坐标x, 按钮中心坐标y, 是否需要文字验证)，未找到返回None
        """
        try:
            self.logger.info("使用改进的图像处理方法查找按钮")

            # 转换为灰度图
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            height, width = gray.shape

            self.logger.info(f"截图尺寸: {width}x{height}")

            # 首先尝试在底部区域查找真正的按钮
            button_result = self._find_button_in_bottom_area(screenshot, gray)
            if button_result:
                button_pos, needs_verification = button_result
                return (button_pos[0], button_pos[1], needs_verification)

            # 如果底部没找到按钮，返回None
            self.logger.warning("底部区域未找到按钮")

            return None

        except Exception as e:
            self.logger.error(f"图像处理查找按钮失败: {e}")

        return None

    def _find_button_in_bottom_area(self, screenshot: np.ndarray, gray: np.ndarray) -> Optional[Tuple[Tuple[int, int], bool]]:
        """
        在底部245像素区域查找"添加到通讯录"按钮（优化版）

        Args:
            screenshot: 原始截图
            gray: 灰度图

        Returns:
            按钮中心坐标，未找到返回None
        """
        height, _ = gray.shape

        # 优化：重点搜索底部245像素区域
        bottom_region_height = min(245, height)
        bottom_start = height - bottom_region_height
        bottom_region = gray[bottom_start:, :]

        self.logger.info(f"搜索底部245像素区域: Y={bottom_start}-{height} (高度:{bottom_region_height})")

        # 特别关注距离底部154像素的位置（Y坐标范围130-160）
        special_y_global = height - 154
        special_y_range = 30  # ±30像素范围
        special_y_start = max(bottom_start, special_y_global - special_y_range)
        special_y_end = min(height, special_y_global + special_y_range)

        if special_y_start < special_y_end:
            self.logger.info(f"特别关注区域: Y={special_y_start}-{special_y_end} (距底部154像素)")

        # 使用优化的边缘检测参数，专门适配微信界面的灰色按钮文字
        edges = cv2.Canny(bottom_region, 15, 60)  # 进一步降低阈值以检测灰色文字
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 保存底部区域边缘检测调试图像
        if self.debug_mode:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            bottom_edge_debug_path = os.path.join(self.screenshot_dir, f"bottom_edge_detection_{timestamp}.png")
            cv2.imwrite(bottom_edge_debug_path, edges)
            self.logger.debug(f"保存底部边缘检测调试图像: {bottom_edge_debug_path}")

        self.logger.info(f"底部区域原始检测到 {len(contours)} 个轮廓")

        button_candidates = []

        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 调整y坐标到全图坐标系
            y_global = y + bottom_start
            area = w * h
            aspect_ratio = w / h if h > 0 else 0

            # 优化的按钮特征检测，专门针对"添加到通讯录"按钮：
            # 1. 宽度30-130像素（适应不同分辨率下中文5字按钮的实际宽度变化）
            # 2. 高度15-40像素（适合中文字符高度）
            # 3. 长宽比3-8（横向文字按钮）
            # 4. 面积750-6000（合理的文字按钮面积）
            # 5. 文字实际长度70-95像素（"添加到通讯录"5个中文字符的实际像素长度）

            # 检查是否为已知的"添加到通讯录"按钮特征
            is_known_button = (
                90 <= w <= 140 and 25 <= h <= 40 and  # 接近已知尺寸 131x33
                special_y_start <= y_global <= special_y_end and  # 在特殊位置
                3.0 <= aspect_ratio <= 5.0  # 接近已知长宽比 3.97
            )

            # 只记录重要的轮廓信息
            if is_known_button or (90 <= w <= 140 and 25 <= h <= 40):
                self.logger.info(f"重要轮廓: 位置({x},{y_global}), 尺寸{w}x{h}, 长宽比{aspect_ratio:.2f}, 已知特征:{is_known_button}")
            else:
                self.logger.debug(f"底部轮廓: 位置({x},{y_global}), 尺寸{w}x{h}, 长宽比{aspect_ratio:.2f}, 面积{area}")

            # 基本筛选：排除明显不是文字的区域
            basic_filter = (10 <= w <= 200 and 8 <= h <= 60 and  # 放宽基本尺寸
                          1.0 <= aspect_ratio <= 20 and 80 <= area <= 12000)  # 放宽长宽比和面积

            if basic_filter:

                roi = gray[y_global:y_global+h, x:x+w]
                mean_intensity = np.mean(roi)
                std_intensity = np.std(roi)

                # 计算位置得分，特殊位置得分更高
                position_score = 1.0
                if special_y_start <= y_global <= special_y_end:
                    position_score = 3.0  # 特殊位置最高分
                    self.logger.info(f"发现特殊位置按钮候选: Y={y_global} (距底部154像素区域)")
                elif y_global >= bottom_start + bottom_region_height * 0.5:
                    position_score = 2.0  # 底部下半区域高分

                # 检查是否符合"添加到通讯录"的精确特征
                # 根据实际观察，"添加到通讯录"按钮的实际尺寸约为128x30
                target_size_ok = (30 <= w <= 140 and 15 <= h <= 40 and
                                3 <= aspect_ratio <= 8 and 750 <= area <= 6000)
                # 扩大文字长度范围以包含实际的128像素宽度
                text_length_ok = (70 <= w <= 135)
                is_target_candidate = target_size_ok and text_length_ok

                # 检查是否为绿色按钮（"添加到通讯录"按钮通常是绿色的）
                is_green_button = self._is_green_button(roi)

                # 对于特殊位置、绿色按钮或已知按钮特征，进一步放宽颜色要求
                if special_y_start <= y_global <= special_y_end or is_green_button or is_known_button:
                    color_threshold = 100  # 特殊情况使用非常宽松的阈值
                    self.logger.debug(f"特殊位置/绿色按钮/已知特征，使用宽松颜色阈值: {color_threshold}")
                else:
                    color_threshold = 60  # 普通位置使用稍微宽松的阈值

                color_ok = std_intensity < color_threshold

                if not color_ok:
                    self.logger.debug(f"颜色变化过大，跳过: std={std_intensity:.1f} (阈值:{color_threshold})")
                    continue

                if color_ok:
                    # 为特殊情况增加额外得分
                    if is_green_button:
                        position_score += 1.0  # 绿色按钮额外加分
                        self.logger.info(f"发现绿色按钮候选: 位置({x},{y_global}), 尺寸{w}x{h}")

                    if is_known_button:
                        position_score += 2.0  # 已知按钮特征额外加分更多
                        self.logger.info(f"发现已知按钮特征候选: 位置({x},{y_global}), 尺寸{w}x{h}")

                    button_candidates.append({
                        'x': x, 'y': y_global, 'w': w, 'h': h,
                        'area': area, 'aspect_ratio': aspect_ratio,
                        'mean_intensity': mean_intensity,
                        'std_intensity': std_intensity,
                        'position_score': position_score,
                        'type': 'bottom_button',
                        'in_special_region': special_y_start <= y_global <= special_y_end,
                        'is_target_candidate': is_target_candidate,
                        'target_size_ok': target_size_ok,
                        'text_length_ok': text_length_ok,
                        'is_green_button': is_green_button,
                        'is_known_button': is_known_button
                    })

                    if is_target_candidate:
                        self.logger.info(f"发现目标候选: 位置({x},{y_global}), 尺寸{w}x{h}")
                    elif is_known_button:
                        self.logger.info(f"发现已知按钮特征: 位置({x},{y_global}), 尺寸{w}x{h}")

    # ==================== 联系人识别功能 ====================

    def capture_wechat_window(self) -> Optional[np.ndarray]:
        """获取微信主窗口截图 - 优化版（集成window_manager和精确截图）"""
        try:
            self.logger.info("🔍 开始优化的微信窗口截图流程...")

            # 步骤1：使用window_manager确保微信窗口激活
            if not self._ensure_wechat_window_active():
                self.logger.error("❌ 无法激活微信窗口")
                return None

            # 步骤2：等待窗口稳定
            self._wait_for_window_stable()

            # 步骤3：获取精确的客户端区域截图
            screenshot = self._capture_client_area_screenshot()
            if screenshot is not None:
                # 步骤4：验证截图质量
                if self._validate_screenshot_quality(screenshot):
                    self.logger.info("✅ 成功获取高质量微信窗口截图")

                    # 保存调试截图
                    if self.debug_mode:
                        self._save_debug_screenshot(screenshot, "optimized_wechat_window")

                    return screenshot
                else:
                    self.logger.warning("⚠️ 截图质量验证失败，尝试重新截图")
                    # 重试一次
                    time.sleep(0.5)
                    screenshot = self._capture_client_area_screenshot()
                    if screenshot is not None and self._validate_screenshot_quality(screenshot):
                        self.logger.info("✅ 重试截图成功")

                        # 保存调试截图
                        if self.debug_mode:
                            self._save_debug_screenshot(screenshot, "optimized_wechat_window_retry")

                        return screenshot

            self.logger.error("❌ 微信窗口截图失败")
            return None

        except Exception as e:
            self.logger.error(f"❌ 获取微信窗口截图失败: {e}")
            return None

    def _ensure_wechat_window_active(self) -> bool:
        """使用window_manager确保微信窗口激活"""
        try:
            self.logger.info("🔄 使用window_manager激活微信窗口...")

            # 导入window_manager模块
            try:
                from window_manager import WeChatWindowManager
                window_manager = WeChatWindowManager()

                # 查找微信窗口
                windows = window_manager.find_all_wechat_windows()
                if not windows:
                    self.logger.error("❌ 未找到任何微信窗口")
                    return False

                # 选择主窗口
                main_window = None
                for window in windows:
                    if window.get('is_main', False) or '微信' in window.get('title', ''):
                        main_window = window
                        break

                if not main_window:
                    main_window = windows[0]  # 使用第一个窗口

                hwnd = main_window.get('hwnd')
                if hwnd is None:
                    self.logger.error("❌ 无法获取微信窗口句柄")
                    return False

                self.logger.info(f"🎯 选择微信窗口: {main_window.get('title', '未知')} (句柄: {hwnd})")

                # 激活窗口
                success = window_manager.activate_window(hwnd)
                if success:
                    self.logger.info("✅ 微信窗口激活成功")

                    # 验证窗口是否在前台
                    import win32gui
                    foreground_hwnd = win32gui.GetForegroundWindow()
                    if foreground_hwnd == hwnd:
                        self.logger.info("✅ 微信窗口已在前台")
                        return True
                    else:
                        self.logger.warning("⚠️ 微信窗口激活但未在前台，尝试强制置前")
                        # 强制置前
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.3)
                        return win32gui.GetForegroundWindow() == hwnd
                else:
                    self.logger.error("❌ 微信窗口激活失败")
                    return False

            except ImportError:
                self.logger.warning("⚠️ 无法导入window_manager，使用备选方案")
                return self._fallback_activate_wechat()

        except Exception as e:
            self.logger.error(f"❌ 激活微信窗口异常: {e}")
            return self._fallback_activate_wechat()

    def _fallback_activate_wechat(self) -> bool:
        """备选的微信窗口激活方案"""
        try:
            # 使用pygetwindow激活
            wechat_windows = gw.getWindowsWithTitle('微信')
            if not wechat_windows:
                return False

            main_window = wechat_windows[0]
            main_window.activate()
            time.sleep(0.5)

            # 验证激活状态
            if main_window.isActive:
                self.logger.info("✅ 备选方案激活微信窗口成功")
                return True
            else:
                self.logger.warning("⚠️ 备选方案激活失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 备选激活方案失败: {e}")
            return False

    def _wait_for_window_stable(self):
        """等待窗口稳定"""
        try:
            self.logger.info("⏳ 等待微信窗口稳定...")

            # 等待窗口动画完成和界面稳定
            time.sleep(1.0)

            # 检查窗口是否还在前台
            import win32gui
            foreground_title = win32gui.GetWindowText(win32gui.GetForegroundWindow())
            if '微信' in foreground_title:
                self.logger.info("✅ 微信窗口保持稳定状态")
            else:
                self.logger.warning(f"⚠️ 前台窗口已变更: {foreground_title}")

        except Exception as e:
            self.logger.warning(f"⚠️ 窗口稳定性检查异常: {e}")

    def _capture_client_area_screenshot(self) -> Optional[np.ndarray]:
        """获取精确的客户端区域截图"""
        try:
            self.logger.info("📸 开始精确客户端区域截图...")

            # 获取微信窗口
            wechat_windows = gw.getWindowsWithTitle('微信')
            if not wechat_windows:
                self.logger.warning("⚠️ 未找到微信窗口")
                return None

            main_window = wechat_windows[0]
            self.logger.info(f"📱 微信窗口信息: 位置({main_window.left}, {main_window.top}) 尺寸{main_window.width}x{main_window.height}")

            # 方法1：尝试获取客户端区域
            try:
                import win32gui
                import win32con

                # 查找微信窗口句柄
                hwnd = None
                for window in gw.getAllWindows():
                    if window.title == '微信' and hasattr(window, 'visible') and window.visible:
                        # 通过窗口位置匹配找到对应的句柄
                        def enum_windows_callback(h, windows):
                            if win32gui.IsWindowVisible(h):
                                try:
                                    rect = win32gui.GetWindowRect(h)
                                    if (abs(rect[0] - window.left) < 10 and
                                        abs(rect[1] - window.top) < 10):
                                        windows.append(h)
                                except:
                                    pass
                            return True

                        windows_list = []
                        win32gui.EnumWindows(enum_windows_callback, windows_list)
                        if windows_list:
                            hwnd = windows_list[0]
                            break

                if hwnd:
                    # 获取客户端区域
                    client_rect = win32gui.GetClientRect(hwnd)
                    client_point = win32gui.ClientToScreen(hwnd, (0, 0))

                    # 计算客户端区域的屏幕坐标
                    client_left = client_point[0]
                    client_top = client_point[1]
                    client_width = client_rect[2] - client_rect[0]
                    client_height = client_rect[3] - client_rect[1]

                    self.logger.info(f"📐 客户端区域: ({client_left}, {client_top}) 尺寸: {client_width}x{client_height}")

                    # 验证客户端区域尺寸
                    if client_width > 100 and client_height > 100:
                        # 截取客户端区域
                        screenshot = pyautogui.screenshot(region=(client_left, client_top, client_width, client_height))
                        screenshot_np = np.array(screenshot)
                        screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                        self.logger.info("✅ 客户端区域截图完成")
                        return screenshot_bgr
                    else:
                        self.logger.warning(f"⚠️ 客户端区域尺寸异常: {client_width}x{client_height}")

            except Exception as e:
                self.logger.warning(f"⚠️ 客户端区域截图方法失败: {e}")

            # 方法2：备选方案 - 使用窗口整体截图
            self.logger.info("🔄 使用备选截图方案...")
            try:
                # 使用窗口整体区域，但排除边框
                border_offset = 8  # 估计的边框宽度
                title_height = 30  # 估计的标题栏高度

                # 计算实际内容区域
                content_left = main_window.left + border_offset
                content_top = main_window.top + title_height
                content_width = main_window.width - 2 * border_offset
                content_height = main_window.height - title_height - border_offset

                self.logger.info(f"📐 备选截图区域: ({content_left}, {content_top}) 尺寸: {content_width}x{content_height}")

                if content_width > 100 and content_height > 100:
                    screenshot = pyautogui.screenshot(region=(content_left, content_top, content_width, content_height))
                    screenshot_np = np.array(screenshot)
                    screenshot_bgr = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)

                    self.logger.info("✅ 备选截图完成")
                    return screenshot_bgr
                else:
                    self.logger.warning(f"⚠️ 备选截图区域尺寸异常: {content_width}x{content_height}")

            except Exception as e:
                self.logger.warning(f"⚠️ 备选截图方案失败: {e}")

            # 方法3：最后备选 - 使用原有截图方法
            self.logger.info("🔄 使用原有截图方法...")
            return self.capture_window_screenshot(main_window)

        except Exception as e:
            self.logger.error(f"❌ 精确截图失败: {e}")
            return None

    def _validate_screenshot_quality(self, screenshot: np.ndarray) -> bool:
        """验证截图质量"""
        try:
            if screenshot is None or screenshot.size == 0:
                return False

            height, width = screenshot.shape[:2]

            # 检查尺寸合理性
            if width < 400 or height < 300:
                self.logger.warning(f"⚠️ 截图尺寸过小: {width}x{height}")
                return False

            # 检查图像是否过于模糊
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()

            if laplacian_var < 50:  # 模糊阈值
                self.logger.warning(f"⚠️ 截图可能模糊，清晰度: {laplacian_var:.2f}")
                return False

            # 检查是否为纯色图像
            std_dev = cv2.meanStdDev(gray)[1][0][0]  # 使用OpenCV计算标准差
            if std_dev < 10:
                self.logger.warning(f"⚠️ 截图可能为纯色，标准差: {std_dev:.2f}")
                return False

            self.logger.info(f"✅ 截图质量验证通过: 尺寸{width}x{height}, 清晰度{laplacian_var:.2f}, 标准差{std_dev:.2f}")
            return True

        except Exception as e:
            self.logger.error(f"❌ 截图质量验证异常: {e}")
            return False

    def _save_debug_screenshot(self, screenshot: np.ndarray, prefix: str):
        """保存调试截图"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_path = os.path.join(self.screenshot_dir, f"{prefix}_{timestamp}.png")
            cv2.imwrite(debug_path, screenshot)
            self.logger.debug(f"💾 保存调试截图: {debug_path}")
        except Exception as e:
            self.logger.warning(f"⚠️ 保存调试截图失败: {e}")

    def find_contacts_in_screenshot(self, screenshot: np.ndarray) -> List[Dict]:
        """
        在截图中识别联系人列表 - 优化版（优先非OCR方案）

        Args:
            screenshot: 微信窗口截图

        Returns:
            联系人信息列表，每个元素包含name, coord, confidence等信息
        """
        try:
            self.logger.info("🔍 开始优化的联系人识别流程...")

            contacts = []

            # 优先方案1：轮廓检测识别联系人条目（快速，无需外部依赖）
            contour_contacts = self._find_contacts_by_contour_optimized(screenshot)
            if contour_contacts:
                contacts.extend(contour_contacts)
                self.logger.info(f"✅ 轮廓检测识别到 {len(contour_contacts)} 个联系人")

            # 优先方案2：模板匹配识别联系人区域（快速，基于图像特征）
            template_contacts = self._find_contacts_by_template_optimized(screenshot)
            if template_contacts:
                # 合并结果，避免重复
                contacts = self._merge_contact_results(contacts, template_contacts)
                self.logger.info(f"✅ 模板匹配识别到 {len(template_contacts)} 个联系人")

            # 备选方案：OCR文字识别（仅在前两种方案结果不足时使用）
            if len(contacts) < 2:  # 如果识别到的联系人少于2个，尝试OCR
                self.logger.info("🔍 联系人识别结果不足，尝试OCR补充...")
                try:
                    ocr_contacts = self._find_contacts_by_ocr_fast(screenshot)
                    if ocr_contacts:
                        contacts = self._merge_contact_results(contacts, ocr_contacts)
                        self.logger.info(f"✅ OCR补充识别到 {len(ocr_contacts)} 个联系人")
                except Exception as ocr_error:
                    self.logger.warning(f"⚠️ OCR识别跳过: {ocr_error}")

            # 过滤和排序结果
            filtered_contacts = self._filter_and_sort_contacts_optimized(contacts)

            self.logger.info(f"🎯 最终识别到 {len(filtered_contacts)} 个有效联系人")

            # 保存调试信息
            if self.debug_mode:
                self._save_contact_debug_info(screenshot, filtered_contacts)

            return filtered_contacts

        except Exception as e:
            self.logger.error(f"❌ 联系人识别失败: {e}")
            return []

    def _find_contacts_by_contour_optimized(self, screenshot: np.ndarray) -> List[Dict]:
        """优化的轮廓检测联系人识别 - 专注检测联系人文字，排除按钮"""
        try:
            self.logger.info("🔍 开始优化轮廓检测（专门针对联系人列表）...")

            # 直接使用专门的联系人列表检测
            list_contacts = self._find_contacts_by_list_items(screenshot)

            if len(list_contacts) > 0:
                self.logger.info(f"✅ 联系人列表检测成功找到 {len(list_contacts)} 个条目")
                return list_contacts

            self.logger.warning("⚠️ 联系人列表检测未找到联系人")
            return []
            contacts = []
            height, width = screenshot.shape[:2]

            # 定义联系人文字区域（专注窗口上部分）
            # 重点搜索窗口上部分，联系人通常在这个区域
            roi_x = int(width * 0.02)  # 左边距2%（扩大搜索范围）
            roi_y = int(height * 0.05)  # 顶部5%（从更靠上开始）
            roi_w = int(width * 0.70)  # 宽度70%（扩大搜索宽度）
            roi_h = int(height * 0.50) # 高度50%（专注上半部分）

            # 确保ROI在图像范围内
            roi_x = max(0, min(roi_x, width - 100))
            roi_y = max(0, min(roi_y, height - 100))
            roi_w = min(roi_w, width - roi_x)
            roi_h = min(roi_h, height - roi_y)

            roi = screenshot[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

            self.logger.info(f"📐 联系人文字识别区域: ({roi_x}, {roi_y}) 尺寸: {roi_w}x{roi_h}")

            # 转换为灰度图
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 专门针对联系人文字的图像处理
            # 1. 高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # 2. 自适应阈值处理 - 更适合文字检测
            thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 15, 4)

            # 3. 形态学操作 - 专门连接文字像素
            kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 1))  # 水平连接
            morphed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel_horizontal)

            # 4. 查找轮廓
            contours, _ = cv2.findContours(morphed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            self.logger.info(f"🔍 找到 {len(contours)} 个轮廓")

            # 5. 分析轮廓特征 - 专注联系人文字，排除按钮
            analyzed_count = 0
            passed_size_filter = 0
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                analyzed_count += 1

                # 记录所有轮廓的基本信息
                if analyzed_count <= 10:  # 只记录前10个避免日志过多
                    self.logger.debug(f"轮廓{analyzed_count}: 位置({x}, {y}) 尺寸{w}x{h}")

                # 联系人文字的特征过滤（排除按钮特征）
                if self._is_contact_text_contour(x, y, w, h, roi_w, roi_h):
                    passed_size_filter += 1
                    # 提取该区域进行详细分析
                    roi_patch = roi[y:y+h, x:x+w]

                    # 检查是否为联系人文字（排除按钮文字）
                    if self._is_contact_text_not_button(roi_patch, w, h):
                        # 转换为全局坐标
                        global_x = roi_x + x + w // 2
                        global_y = roi_y + y + h // 2

                        # 计算联系人文字置信度
                        confidence = self._calculate_contact_text_confidence(
                            contour, w, h, roi_patch
                        )

                        if confidence > 15:  # 进一步降低置信度阈值
                            contacts.append({
                                'name': f'联系人_{len(contacts)+1}',
                                'coord': (global_x, global_y),
                                'confidence': confidence,
                                'source': 'contour_contact_text',
                                'bbox': (global_x - w//2, global_y - h//2, w, h)
                            })

                            self.logger.debug(f"� 联系人文字: 坐标({global_x}, {global_y}) 置信度{confidence} 尺寸{w}x{h}")

            # 按置信度排序
            contacts.sort(key=lambda x: x['confidence'], reverse=True)

            self.logger.info(f"📊 轮廓分析统计: 总轮廓{analyzed_count} -> 通过尺寸过滤{passed_size_filter} -> 最终联系人{len(contacts)}")
            self.logger.info(f"✅ 轮廓检测找到 {len(contacts)} 个联系人文字（专注窗口上部分）")
            return contacts[:6]  # 限制最多6个

        except Exception as e:
            self.logger.error(f"❌ 优化轮廓检测失败: {e}")
            return []







    def _find_contacts_by_list_items(self, screenshot: np.ndarray) -> List[Dict]:
        """专门检测微信联系人列表条目的轮廓检测"""
        try:
            self.logger.info("📋 开始联系人列表条目检测...")

            contacts = []
            height, width = screenshot.shape[:2]

            # 专门针对联系人列表的识别区域（左侧列表区域）
            roi_x = 0
            roi_y = 0
            roi_w = int(width * 0.4)  # 左侧40%宽度，专注列表区域
            roi_h = height  # 整个高度

            roi = screenshot[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]
            self.logger.info(f"📐 联系人列表识别区域: ({roi_x}, {roi_y}) 尺寸: {roi_w}x{roi_h}")

            # 转换为灰度图
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 使用边缘检测来找到条目边界
            # 先进行高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # 使用Canny边缘检测
            edges = cv2.Canny(blurred, 30, 100)

            # 水平形态学操作，连接同一行的边缘
            kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 1))
            edges_horizontal = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_horizontal)

            # 垂直形态学操作，连接条目的上下边界
            kernel_vertical = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 5))
            edges_processed = cv2.morphologyEx(edges_horizontal, cv2.MORPH_CLOSE, kernel_vertical)

            # 保存调试图像
            if self.debug_mode:
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                cv2.imwrite(f"screenshots/list_edges_{timestamp}.png", edges_processed)

            # 查找轮廓
            contours, _ = cv2.findContours(edges_processed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            self.logger.info(f"🔍 找到 {len(contours)} 个轮廓")

            # 创建轮廓标记图像用于调试 - 只标记选中的最后一个轮廓
            if self.debug_mode:
                contour_debug_img = cv2.cvtColor(roi, cv2.COLOR_BGR2RGB)

                # 收集所有有效轮廓
                debug_valid_contours = []
                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)
                    if self._is_contact_list_item(x, y, w, h, roi_w, roi_h):
                        debug_valid_contours.append({
                            'bbox': (x, y, w, h),
                            'y_coord': y
                        })

                # 按Y坐标排序并只标记最后一个
                if debug_valid_contours:
                    debug_valid_contours.sort(key=lambda x: x['y_coord'])
                    last_contour = debug_valid_contours[-1]
                    x, y, w, h = last_contour['bbox']

                    # 只绘制选中的最后一个轮廓（绿色粗框）
                    color = (0, 255, 0)  # 绿色
                    thickness = 3
                    cv2.rectangle(contour_debug_img, (x, y), (x + w, y + h), color, thickness)

                    # 添加标签
                    label = f"SELECTED:{w}x{h}"
                    cv2.putText(contour_debug_img, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 2)

                # 保存标记后的图像
                debug_path = f"screenshots/selected_contact_{timestamp}.png"
                cv2.imwrite(debug_path, cv2.cvtColor(contour_debug_img, cv2.COLOR_RGB2BGR))
                self.logger.info(f"💾 保存选中联系人标记图像: {debug_path}")

            # 过滤和识别联系人条目 - 收集所有符合条件的轮廓
            valid_contours = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # 检查是否符合联系人条目特征
                if self._is_contact_list_item(x, y, w, h, roi_w, roi_h):
                    # 计算置信度
                    confidence = self._calculate_list_item_confidence(w, h, roi_w)

                    valid_contours.append({
                        'contour': contour,
                        'bbox': (x, y, w, h),
                        'confidence': confidence,
                        'y_coord': y  # 用于排序
                    })

                    self.logger.debug(f"📋 有效轮廓: 位置({x}, {y}) 尺寸{w}x{h} 置信度{confidence}")

            # 按Y坐标排序（从上到下）
            valid_contours.sort(key=lambda x: x['y_coord'])

            self.logger.info(f"🔍 找到 {len(valid_contours)} 个有效轮廓")

            # 只保留最后一个轮廓（最下方的条目）
            if valid_contours:
                last_contour = valid_contours[-1]  # 最后一个（最下方）
                x, y, w, h = last_contour['bbox']
                confidence = last_contour['confidence']

                # 转换为全局坐标
                global_x = roi_x + x + w // 2
                global_y = roi_y + y + h // 2

                contacts.append({
                    'name': '联系人',  # 通常最下方的条目是"联系人"
                    'coord': (global_x, global_y),
                    'confidence': confidence,
                    'source': 'list_item_detection_last',
                    'bbox': (global_x - w//2, global_y - h//2, w, h)
                })

                self.logger.info(f"✅ 选择最下方的联系人条目: 位置({x}, {y}) 尺寸{w}x{h}")
                self.logger.info(f"📊 过滤统计: 总轮廓{len(contours)} -> 有效轮廓{len(valid_contours)} -> 最终选择1个")
            else:
                self.logger.info(f"⚠️ 未找到有效的联系人条目")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ 联系人列表检测失败: {e}")
            return []

    def _is_contact_list_item(self, x: int, y: int, w: int, h: int, roi_w: int, roi_h: int) -> bool:
        """判断轮廓是否为联系人列表条目（恢复到成功的版本）"""
        try:
            # 1. 基本尺寸检查 - 恢复到放宽的条件
            if not (30 <= w <= 400 and 10 <= h <= 80):
                self.logger.debug(f"❌ 尺寸不符: {w}x{h}")
                return False

            # 2. 宽高比检查 - 恢复到放宽的条件
            aspect_ratio = w / h
            if not (1.5 <= aspect_ratio <= 20.0):
                self.logger.debug(f"❌ 宽高比不符: {aspect_ratio:.2f}")
                return False

            # 3. 位置检查 - 恢复到放宽的边界要求
            if x < 2 or y < 5:
                self.logger.debug(f"❌ 位置太靠边: ({x}, {y})")
                return False

            # 4. 面积检查 - 恢复到放宽的面积要求
            area = w * h
            if area < 300 or area > 25000:
                self.logger.debug(f"❌ 面积不符: {area}")
                return False

            # 5. 宽度占比检查 - 恢复到降低的要求
            width_ratio = w / roi_w
            if width_ratio < 0.2:  # 至少占20%宽度
                self.logger.debug(f"❌ 宽度占比不足: {width_ratio:.2f}")
                return False

            self.logger.debug(f"✅ 通过过滤: {w}x{h} 比例{aspect_ratio:.2f} 面积{area} 宽度占比{width_ratio:.2f}")
            return True

        except Exception as e:
            self.logger.debug(f"⚠️ 联系人条目判断异常: {e}")
            return False



    def _calculate_list_item_confidence(self, w: int, h: int, roi_w: int) -> int:
        """计算联系人列表条目的置信度"""
        try:
            confidence = 60  # 基础分数

            # 1. 尺寸评分 - 理想的联系人条目尺寸
            if 150 <= w <= 250 and 25 <= h <= 40:
                confidence += 25
            elif 120 <= w <= 300 and 20 <= h <= 45:
                confidence += 15

            # 2. 宽高比评分 - 联系人条目的理想宽高比
            aspect_ratio = w / h
            if 5.0 <= aspect_ratio <= 10.0:
                confidence += 20
            elif 3.0 <= aspect_ratio <= 12.0:
                confidence += 10

            # 3. 宽度占比评分 - 占据列表宽度的比例
            width_ratio = w / roi_w
            if 0.6 <= width_ratio <= 0.9:
                confidence += 15
            elif 0.4 <= width_ratio <= 0.95:
                confidence += 10

            return min(confidence, 100)

        except Exception as e:
            self.logger.debug(f"⚠️ 列表条目置信度计算异常: {e}")
            return 60

    def _is_contact_text_contour(self, x: int, y: int, w: int, h: int, roi_w: int, roi_h: int) -> bool:
        """判断轮廓是否为联系人文字（排除按钮）"""
        try:
            # 1. 尺寸过滤 - 联系人文字的典型尺寸（放宽条件）
            if not (15 <= w <= 200 and 8 <= h <= 40):
                return False

            # 2. 位置过滤 - 避开按钮区域
            # 排除右侧区域（可能有按钮）
            if x + w > roi_w * 0.90:
                return False

            # 排除底部区域（可能有"添加到通讯录"按钮）
            if y + h > roi_h * 0.90:
                return False

            # 排除顶部区域（可能有搜索框）
            if y < roi_h * 0.05:
                return False

            # 3. 宽高比过滤 - 联系人文字通常是横向的（放宽条件）
            aspect_ratio = w / h
            if not (0.8 <= aspect_ratio <= 8.0):
                return False

            # 4. 位置合理性 - 应该在左侧主要区域（放宽条件）
            if x < 3 or x > roi_w * 0.85:
                return False

            # 5. 排除过小的轮廓（可能是噪声）
            if w * h < 100:
                return False

            self.logger.debug(f"🔍 候选轮廓: 位置({x}, {y}) 尺寸{w}x{h} 宽高比{aspect_ratio:.2f}")
            return True

        except Exception as e:
            self.logger.debug(f"⚠️ 联系人文字轮廓判断异常: {e}")
            return False

    def _is_contact_text_not_button(self, roi_patch: np.ndarray, w: int, h: int) -> bool:
        """检测是否为联系人文字而非按钮文字（放宽条件）"""
        try:
            if roi_patch.size == 0:
                return False

            # 转换为灰度图
            if len(roi_patch.shape) == 3:
                gray = cv2.cvtColor(roi_patch, cv2.COLOR_BGR2GRAY)
            else:
                gray = roi_patch

            # 1. 基本尺寸检查
            if gray.shape[0] < 5 or gray.shape[1] < 5:
                return False

            # 2. 文字密度检测（放宽条件）
            # 联系人文字应该有适中的像素密度
            dark_pixel_ratio = np.count_nonzero(gray < 150) / gray.size
            if not (0.05 <= dark_pixel_ratio <= 0.8):
                self.logger.debug(f"⚠️ 像素密度不符合: {dark_pixel_ratio:.3f}")
                return False

            # 3. 文字纹理检测（降低阈值）
            # 使用Laplacian算子检测文字纹理
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            if laplacian_var < 10:  # 降低纹理变化要求
                self.logger.debug(f"⚠️ 纹理变化不足: {laplacian_var:.2f}")
                return False

            # 4. 水平文字特征（放宽条件）
            # 联系人名字通常是水平排列的文字
            if w > h * 1.2:  # 降低宽高比要求
                self.logger.debug(f"✅ 水平文字特征: {w}x{h} 比例{w/h:.2f}")
                return True

            # 5. 边缘密度检测（放宽条件）
            edges = cv2.Canny(gray, 20, 80)  # 降低边缘检测阈值
            edge_ratio = np.sum(edges > 0) / edges.size
            if 0.01 <= edge_ratio <= 0.4:  # 放宽边缘密度范围
                self.logger.debug(f"✅ 边缘密度合适: {edge_ratio:.3f}")
                return True

            # 6. 背景检测（简化）
            mean_intensity = float(gray.mean())
            if 30 <= mean_intensity <= 220:  # 合理的亮度范围
                self.logger.debug(f"✅ 亮度合理: {mean_intensity:.1f}")
                return True

            self.logger.debug(f"⚠️ 未通过文字检测: 密度{dark_pixel_ratio:.3f} 纹理{laplacian_var:.2f} 边缘{edge_ratio:.3f}")
            return False

        except Exception as e:
            self.logger.debug(f"⚠️ 联系人文字检测异常: {e}")
            return False

    def _calculate_contact_text_confidence(self, contour, width: int, height: int, roi_patch: np.ndarray) -> int:
        """计算联系人文字的置信度（排除按钮）"""
        try:
            confidence = 45  # 基础分数

            # 1. 尺寸评分 - 联系人文字的理想尺寸
            if 40 <= width <= 100 and 15 <= height <= 25:
                confidence += 25
            elif 25 <= width <= 120 and 12 <= height <= 30:
                confidence += 15

            # 2. 宽高比评分 - 联系人文字通常是横向的
            aspect_ratio = width / height
            if 2.0 <= aspect_ratio <= 5.0:
                confidence += 20
            elif 1.5 <= aspect_ratio <= 6.0:
                confidence += 10

            # 3. 轮廓复杂度评分
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                area = cv2.contourArea(contour)
                compactness = 4 * np.pi * area / (perimeter * perimeter)
                if 0.15 <= compactness <= 0.7:  # 文字轮廓的合理复杂度
                    confidence += 15

            # 4. 文字纹理评分
            if roi_patch.size > 0:
                if len(roi_patch.shape) == 3:
                    gray_patch = cv2.cvtColor(roi_patch, cv2.COLOR_BGR2GRAY)
                else:
                    gray_patch = roi_patch

                # 文字纹理变化
                texture_var = cv2.Laplacian(gray_patch, cv2.CV_64F).var()
                if texture_var > 40:
                    confidence += 15
                elif texture_var > 20:
                    confidence += 8

                # 边缘密度
                edges = cv2.Canny(gray_patch, 30, 100)
                edge_ratio = np.sum(edges > 0) / edges.size
                if 0.05 <= edge_ratio <= 0.2:
                    confidence += 10

                # 像素密度（排除按钮的均匀背景）
                dark_ratio = np.count_nonzero(gray_patch < 128) / gray_patch.size
                if 0.2 <= dark_ratio <= 0.6:
                    confidence += 10

            return min(confidence, 100)

        except Exception as e:
            self.logger.debug(f"⚠️ 联系人文字置信度计算异常: {e}")
            return 45

    def _calculate_contour_confidence(self, contour, width: int, height: int,
                                    area_ratio: float, roi_patch: np.ndarray) -> int:
        """计算轮廓的置信度"""
        try:
            confidence = 50  # 基础分数

            # 1. 尺寸评分（理想的联系人条目尺寸）
            if 60 <= width <= 150 and 20 <= height <= 35:
                confidence += 20
            elif 40 <= width <= 200 and 15 <= height <= 40:
                confidence += 10

            # 2. 面积比例评分
            if 0.2 <= area_ratio <= 0.5:
                confidence += 15
            elif 0.1 <= area_ratio <= 0.7:
                confidence += 5

            # 3. 轮廓复杂度评分
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                compactness = 4 * np.pi * cv2.contourArea(contour) / (perimeter * perimeter)
                if 0.1 <= compactness <= 0.8:
                    confidence += 10

            # 4. 图像纹理评分
            if roi_patch.size > 0:
                texture_var = np.var(roi_patch)
                if texture_var > 100:  # 有足够的纹理变化
                    confidence += 10

            return min(confidence, 100)  # 限制最大值

        except Exception as e:
            self.logger.debug(f"⚠️ 置信度计算异常: {e}")
            return 50

    def _find_contacts_by_template_optimized(self, screenshot: np.ndarray) -> List[Dict]:
        """优化的模板匹配联系人识别"""
        try:
            self.logger.info("🔍 开始优化模板匹配...")

            contacts = []
            height, width = screenshot.shape[:2]

            # 定义通讯录区域（基于微信界面布局）
            roi_x = int(width * 0.05)  # 左边距5%
            roi_y = int(height * 0.15)  # 顶部15%
            roi_w = int(width * 0.6)   # 宽度60%
            roi_h = int(height * 0.7)  # 高度70%

            # 确保ROI在图像范围内
            roi_x = max(0, min(roi_x, width - 100))
            roi_y = max(0, min(roi_y, height - 100))
            roi_w = min(roi_w, width - roi_x)
            roi_h = min(roi_h, height - roi_y)

            roi = screenshot[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

            self.logger.info(f"📐 模板匹配区域: ({roi_x}, {roi_y}) 尺寸: {roi_w}x{roi_h}")

            # 转换为灰度图
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 使用边缘检测识别矩形区域（联系人条目通常是矩形）
            edges = cv2.Canny(gray, 30, 100)

            # 形态学操作连接断开的边缘
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 1))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # 联系人条目的特征过滤
                if (50 <= w <= 250 and      # 宽度范围
                    18 <= h <= 45 and       # 高度范围
                    x > 10 and              # 左边距
                    y > 15 and              # 顶部距离
                    x + w < roi_w - 10):    # 右边距

                    # 转换为全局坐标
                    global_x = roi_x + x + w // 2
                    global_y = roi_y + y + h // 2

                    # 提取该区域进行文字特征检测
                    roi_patch = roi[y:y+h, x:x+w]

                    # 检查是否包含文字特征
                    if self._has_text_features_optimized(roi_patch):
                        # 计算置信度
                        confidence = self._calculate_template_confidence(contour, w, h, roi_patch)

                        if confidence > 40:  # 置信度阈值
                            contacts.append({
                                'name': f'联系人_{len(contacts)+1}',
                                'coord': (global_x, global_y),
                                'confidence': confidence,
                                'source': 'template_optimized',
                                'bbox': (global_x - w//2, global_y - h//2, w, h)
                            })

                            self.logger.debug(f"🔍 模板联系人: 坐标({global_x}, {global_y}) 置信度{confidence}")

            # 按置信度排序
            contacts.sort(key=lambda x: x['confidence'], reverse=True)

            return contacts[:6]  # 限制最多6个

        except Exception as e:
            self.logger.error(f"❌ 优化模板匹配失败: {e}")
            return []

    def _has_text_features_optimized(self, roi: np.ndarray) -> bool:
        """优化的文字特征检测"""
        try:
            if roi.size == 0:
                return False

            # 转换为灰度图
            if len(roi.shape) == 3:
                gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray = roi

            # 计算图像的方差（文字区域通常有较高的方差）
            variance = cv2.Laplacian(gray, cv2.CV_64F).var()

            # 检查边缘密度
            edges = cv2.Canny(gray, 30, 100)
            edge_ratio = np.sum(edges > 0) / edges.size

            # 检查亮度分布
            mean_intensity = gray.mean()

            # 文字区域的特征：适中的方差、边缘密度和亮度
            return (variance > 50 and
                   0.01 < edge_ratio < 0.4 and
                   50 < mean_intensity < 200)

        except Exception as e:
            self.logger.debug(f"⚠️ 优化文字特征检测异常: {e}")
            return False

    def _calculate_template_confidence(self, contour, width: int, height: int, roi_patch: np.ndarray) -> int:
        """计算模板匹配的置信度"""
        try:
            confidence = 50  # 基础分数

            # 1. 尺寸评分
            if 80 <= width <= 180 and 22 <= height <= 35:
                confidence += 25
            elif 50 <= width <= 250 and 18 <= height <= 45:
                confidence += 15

            # 2. 轮廓复杂度评分
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                area = cv2.contourArea(contour)
                compactness = 4 * np.pi * area / (perimeter * perimeter)
                if 0.1 <= compactness <= 0.7:
                    confidence += 15

            # 3. 图像纹理评分
            if roi_patch.size > 0:
                if len(roi_patch.shape) == 3:
                    gray_patch = cv2.cvtColor(roi_patch, cv2.COLOR_BGR2GRAY)
                else:
                    gray_patch = roi_patch

                texture_var = cv2.Laplacian(gray_patch, cv2.CV_64F).var()
                if texture_var > 80:
                    confidence += 10

            return min(confidence, 100)

        except Exception as e:
            self.logger.debug(f"⚠️ 模板置信度计算异常: {e}")
            return 50

    def _find_contacts_by_ocr_fast(self, screenshot: np.ndarray) -> List[Dict]:
        """快速OCR联系人识别（优化版，减少依赖）"""
        try:
            self.logger.info("🔍 开始快速OCR识别...")

            contacts = []
            height, width = screenshot.shape[:2]

            # 定义通讯录区域
            roi_x = int(width * 0.05)
            roi_y = int(height * 0.15)
            roi_w = int(width * 0.6)
            roi_h = int(height * 0.7)

            # 确保ROI在图像范围内
            roi_x = max(0, min(roi_x, width - 100))
            roi_y = max(0, min(roi_y, height - 100))
            roi_w = min(roi_w, width - roi_x)
            roi_h = min(roi_h, height - roi_y)

            roi = screenshot[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

            self.logger.info(f"📐 OCR识别区域: ({roi_x}, {roi_y}) 尺寸: {roi_w}x{roi_h}")

            # 优先使用EasyOCR（如果可用）
            if EASYOCR_AVAILABLE:
                try:
                    easyocr_contacts = self._ocr_with_easyocr_fast(roi, roi_x, roi_y)
                    contacts.extend(easyocr_contacts)
                    self.logger.info(f"✅ EasyOCR识别到 {len(easyocr_contacts)} 个联系人")
                except Exception as e:
                    self.logger.warning(f"⚠️ EasyOCR快速识别失败: {e}")

            # 备选使用Tesseract（如果EasyOCR不可用或结果不足）
            if len(contacts) < 2 and OCR_AVAILABLE:
                try:
                    tesseract_contacts = self._ocr_with_tesseract_fast(roi, roi_x, roi_y)
                    contacts.extend(tesseract_contacts)
                    self.logger.info(f"✅ Tesseract识别到 {len(tesseract_contacts)} 个联系人")
                except Exception as e:
                    self.logger.warning(f"⚠️ Tesseract快速识别失败: {e}")

            # 如果都不可用，返回空列表
            if not EASYOCR_AVAILABLE and not OCR_AVAILABLE:
                self.logger.warning("⚠️ 无可用OCR引擎，跳过OCR识别")
                return []

            return contacts[:5]  # 限制最多5个

        except Exception as e:
            self.logger.error(f"❌ 快速OCR识别失败: {e}")
            return []

    def _ocr_with_easyocr_fast(self, roi: np.ndarray, offset_x: int, offset_y: int) -> List[Dict]:
        """快速EasyOCR识别（优化版）"""
        try:
            # 初始化EasyOCR读取器（使用更快的配置）
            reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)  # 禁用GPU加速以提高兼容性

            # 进行OCR识别
            results = reader.readtext(roi, detail=1, paragraph=False)

            contacts = []
            for (bbox, text, confidence) in results:
                text = text.strip()

                if text and float(confidence) > 0.4:  # 稍微提高置信度阈值
                    # 检查是否为有效的联系人名称
                    if self._is_valid_contact_name_fast(text):
                        # 计算边界框中心点
                        bbox_array = np.array(bbox)
                        center_x = int(bbox_array[:, 0].mean()) + offset_x
                        center_y = int(bbox_array[:, 1].mean()) + offset_y

                        # 计算边界框
                        min_x = int(bbox_array[:, 0].min()) + offset_x
                        min_y = int(bbox_array[:, 1].min()) + offset_y
                        max_x = int(bbox_array[:, 0].max()) + offset_x
                        max_y = int(bbox_array[:, 1].max()) + offset_y

                        contacts.append({
                            'name': text,
                            'coord': (center_x, center_y),
                            'confidence': int(confidence * 100),
                            'source': 'easyocr_fast',
                            'bbox': (min_x, min_y, max_x - min_x, max_y - min_y)
                        })

                        self.logger.debug(f"✅ EasyOCR快速识别: '{text}' 置信度:{confidence:.2f} 坐标:({center_x}, {center_y})")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ EasyOCR快速识别失败: {e}")
            return []

    def _ocr_with_tesseract_fast(self, roi: np.ndarray, offset_x: int, offset_y: int) -> List[Dict]:
        """快速Tesseract识别（优化版）"""
        try:
            # 预处理图像以提高OCR准确率
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 简化的图像增强
            enhanced = cv2.equalizeHist(gray)

            # 使用更快的OCR配置
            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'

            # 获取文字和位置信息
            data = pytesseract.image_to_data(enhanced, config=custom_config, output_type=pytesseract.Output.DICT)

            contacts = []
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])

                if text and confidence > 40:  # 稍微提高置信度阈值
                    # 检查是否为有效的联系人名称
                    if self._is_valid_contact_name_fast(text):
                        x = data['left'][i] + offset_x
                        y = data['top'][i] + offset_y
                        w = data['width'][i]
                        h = data['height'][i]

                        center_x = x + w // 2
                        center_y = y + h // 2

                        contacts.append({
                            'name': text,
                            'coord': (center_x, center_y),
                            'confidence': confidence,
                            'source': 'tesseract_fast',
                            'bbox': (x, y, w, h)
                        })

                        self.logger.debug(f"✅ Tesseract快速识别: '{text}' 置信度:{confidence} 坐标:({center_x}, {center_y})")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ Tesseract快速识别失败: {e}")
            return []

    def _is_valid_contact_name_fast(self, text: str) -> bool:
        """快速验证联系人名称（简化版）"""
        if not text or len(text.strip()) == 0:
            return False

        text = text.strip()

        # 基本长度检查
        if not (1 <= len(text) <= 15):
            return False

        # 排除明显的系统文字
        exclude_texts = ['微信', 'WeChat', '搜索', '通讯录', '新的朋友', '群聊', '设置']
        if text in exclude_texts:
            return False

        # 简化的格式检查 - 必须包含字母或汉字
        import re
        if re.search(r'[\u4e00-\u9fa5a-zA-Z]', text):
            return True

        return False

    def _find_contacts_by_ocr(self, screenshot: np.ndarray) -> List[Dict]:
        """使用OCR识别联系人姓名"""
        try:
            contacts = []

            # 定义通讯录区域（通常在窗口的左侧或中间）
            height, width = screenshot.shape[:2]

            # 通讯录区域通常在窗口的左侧，排除顶部标题栏和底部按钮区域
            roi_x = int(width * 0.05)  # 左边距5%
            roi_y = int(height * 0.15)  # 顶部15%
            roi_w = int(width * 0.6)   # 宽度60%
            roi_h = int(height * 0.7)  # 高度70%

            roi = screenshot[roi_y:roi_y+roi_h, roi_x:roi_x+roi_w]

            self.logger.info(f"🔍 OCR识别区域: ({roi_x}, {roi_y}) 尺寸: {roi_w}x{roi_h}")

            # 方案1：使用pytesseract
            if OCR_AVAILABLE:
                tesseract_contacts = self._ocr_with_tesseract(roi, roi_x, roi_y)
                contacts.extend(tesseract_contacts)

            # 方案2：使用easyocr
            if EASYOCR_AVAILABLE:
                easyocr_contacts = self._ocr_with_easyocr(roi, roi_x, roi_y)
                contacts.extend(easyocr_contacts)

            return contacts

        except Exception as e:
            self.logger.error(f"❌ OCR联系人识别失败: {e}")
            return []

    def _ocr_with_tesseract(self, roi: np.ndarray, offset_x: int, offset_y: int) -> List[Dict]:
        """使用Tesseract进行OCR识别"""
        try:
            # 预处理图像以提高OCR准确率
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)

            # 使用中文+英文识别
            custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'

            # 获取文字和位置信息
            data = pytesseract.image_to_data(enhanced, config=custom_config, output_type=pytesseract.Output.DICT)

            contacts = []
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                confidence = int(data['conf'][i])

                if text and confidence > 30:  # 置信度阈值
                    # 检查是否为有效的联系人名称
                    if self._is_valid_contact_name_ocr(text):
                        x = data['left'][i] + offset_x
                        y = data['top'][i] + offset_y
                        w = data['width'][i]
                        h = data['height'][i]

                        center_x = x + w // 2
                        center_y = y + h // 2

                        contacts.append({
                            'name': text,
                            'coord': (center_x, center_y),
                            'confidence': confidence,
                            'source': 'tesseract',
                            'bbox': (x, y, w, h)
                        })

                        self.logger.info(f"✅ Tesseract识别联系人: '{text}' 置信度:{confidence} 坐标:({center_x}, {center_y})")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ Tesseract OCR失败: {e}")
            return []

    def _ocr_with_easyocr(self, roi: np.ndarray, offset_x: int, offset_y: int) -> List[Dict]:
        """使用EasyOCR进行OCR识别"""
        try:
            # 初始化EasyOCR读取器
            reader = easyocr.Reader(['ch_sim', 'en'])

            # 进行OCR识别
            results = reader.readtext(roi)

            contacts = []
            for (bbox, text, confidence) in results:
                text = text.strip()

                if text and float(confidence) > 0.3:  # 置信度阈值
                    # 检查是否为有效的联系人名称
                    if self._is_valid_contact_name_ocr(text):
                        # 计算边界框中心点
                        bbox_array = np.array(bbox)
                        center_x = int(np.mean(bbox_array[:, 0])) + offset_x
                        center_y = int(np.mean(bbox_array[:, 1])) + offset_y

                        # 计算边界框
                        min_x = int(np.min(bbox_array[:, 0])) + offset_x
                        min_y = int(np.min(bbox_array[:, 1])) + offset_y
                        max_x = int(np.max(bbox_array[:, 0])) + offset_x
                        max_y = int(np.max(bbox_array[:, 1])) + offset_y

                        contacts.append({
                            'name': text,
                            'coord': (center_x, center_y),
                            'confidence': int(confidence * 100),
                            'source': 'easyocr',
                            'bbox': (min_x, min_y, max_x - min_x, max_y - min_y)
                        })

                        self.logger.info(f"✅ EasyOCR识别联系人: '{text}' 置信度:{confidence:.2f} 坐标:({center_x}, {center_y})")

            return contacts

        except Exception as e:
            self.logger.error(f"❌ EasyOCR失败: {e}")
            return []

    def _is_valid_contact_name_ocr(self, text: str) -> bool:
        """验证OCR识别的文本是否为有效的联系人名称"""
        if not text or len(text.strip()) == 0:
            return False

        text = text.strip()

        # 排除系统元素和界面文字
        exclude_texts = [
            '微信', 'WeChat', '搜索', '通讯录', '联系人', '新的朋友', '公众号', '服务号',
            '群聊', '标签', '设置', '帮助', '关于', '添加', '删除', '确定', '取消',
            '返回', '刷新', '更多', '全部', '分组', '企业微信联系人', '通讯录管理'
        ]

        # 精确匹配排除
        for exclude in exclude_texts:
            if text == exclude:
                return False

        # 长度检查
        if not (1 <= len(text) <= 20):
            return False

        # 格式检查 - 支持中文、英文、数字、常见符号
        import re
        if re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9\s\-_\.\(\)\[\]]+$', text):
            # 排除纯数字或纯符号
            if not re.match(r'^[\d\s\-_\.]+$', text):
                # 必须包含至少一个字母或汉字
                if re.search(r'[\u4e00-\u9fa5a-zA-Z]', text):
                    return True

        return False

    def _find_contacts_by_template(self, screenshot: np.ndarray) -> List[Dict]:
        """使用模板匹配识别联系人区域"""
        try:
            contacts = []

            # 转换为灰度图
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # 创建联系人条目的模板特征
            # 这里可以预先准备一些联系人条目的模板图像

            # 方案：基于边缘检测识别矩形区域（联系人条目通常是矩形）
            edges = cv2.Canny(gray, 50, 150)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            height, width = screenshot.shape[:2]

            for contour in contours:
                # 获取边界矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 过滤条件：联系人条目的特征
                if (80 <= w <= 300 and  # 宽度范围
                    20 <= h <= 60 and   # 高度范围
                    x > 20 and          # 不在窗口边缘
                    y > 50 and          # 不在标题栏
                    x + w < width - 20 and  # 不超出右边界
                    y + h < height - 50):   # 不在底部按钮区域

                    center_x = x + w // 2
                    center_y = y + h // 2

                    # 提取该区域进行进一步分析
                    roi = screenshot[y:y+h, x:x+w]

                    # 简单的文字检测（检查是否包含文字特征）
                    if self._has_text_features(roi):
                        contacts.append({
                            'name': f'联系人_{len(contacts)+1}',  # 临时名称
                            'coord': (center_x, center_y),
                            'confidence': 70,
                            'source': 'template',
                            'bbox': (x, y, w, h)
                        })

                        self.logger.debug(f"🔍 模板匹配发现联系人区域: 坐标({center_x}, {center_y}) 尺寸{w}x{h}")

            return contacts[:10]  # 限制最多10个

        except Exception as e:
            self.logger.error(f"❌ 模板匹配失败: {e}")
            return []

    def _has_text_features(self, roi: np.ndarray) -> bool:
        """检查区域是否包含文字特征"""
        try:
            if roi.size == 0:
                return False

            # 转换为灰度图
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # 计算图像的方差（文字区域通常有较高的方差）
            variance = cv2.Laplacian(gray, cv2.CV_64F).var()

            # 检查是否有足够的边缘特征
            edges = cv2.Canny(gray, 50, 150)
            edge_ratio = np.sum(edges > 0) / edges.size

            # 文字区域的特征：适中的方差和边缘密度
            return variance > 100 and 0.01 < edge_ratio < 0.3

        except Exception as e:
            self.logger.debug(f"⚠️ 文字特征检测异常: {e}")
            return False

    def _find_contacts_by_contour(self, screenshot: np.ndarray) -> List[Dict]:
        """使用轮廓检测识别联系人条目"""
        try:
            contacts = []

            # 转换为灰度图
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)

            # 应用高斯模糊减少噪声
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # 自适应阈值处理
            thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)

            # 形态学操作，连接文字
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            morphed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(morphed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            height, width = screenshot.shape[:2]

            for contour in contours:
                # 获取边界矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 联系人条目的特征过滤
                if (60 <= w <= 250 and      # 宽度范围
                    15 <= h <= 50 and       # 高度范围
                    x > 30 and              # 左边距
                    y > 80 and              # 顶部距离
                    x + w < width - 30 and  # 右边距
                    y + h < height - 80):   # 底部距离

                    # 计算轮廓面积和边界矩形面积的比例
                    contour_area = cv2.contourArea(contour)
                    rect_area = w * h

                    if rect_area > 0:
                        area_ratio = contour_area / rect_area

                        # 文字轮廓通常有适中的面积比例
                        if 0.1 < area_ratio < 0.8:
                            center_x = x + w // 2
                            center_y = y + h // 2

                            contacts.append({
                                'name': f'轮廓联系人_{len(contacts)+1}',
                                'coord': (center_x, center_y),
                                'confidence': int(area_ratio * 100),
                                'source': 'contour',
                                'bbox': (x, y, w, h)
                            })

                            self.logger.debug(f"🔍 轮廓检测发现联系人: 坐标({center_x}, {center_y}) 面积比例{area_ratio:.2f}")

            return contacts[:8]  # 限制最多8个

        except Exception as e:
            self.logger.error(f"❌ 轮廓检测失败: {e}")
            return []

    def _merge_contact_results(self, contacts1: List[Dict], contacts2: List[Dict]) -> List[Dict]:
        """合并不同方法的联系人识别结果，去除重复"""
        try:
            merged = contacts1.copy()

            for contact2 in contacts2:
                coord2 = contact2['coord']
                is_duplicate = False

                # 检查是否与已有联系人重复（距离阈值30像素）
                for contact1 in merged:
                    coord1 = contact1['coord']
                    distance = ((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)**0.5

                    if distance < 30:
                        is_duplicate = True
                        # 如果新联系人有更好的置信度或更详细的信息，则替换
                        if (contact2.get('confidence', 0) > contact1.get('confidence', 0) or
                            (contact2.get('name', '').startswith('联系人_') == False and
                             contact1.get('name', '').startswith('联系人_'))):
                            # 替换为更好的结果
                            merged[merged.index(contact1)] = contact2
                        break

                if not is_duplicate:
                    merged.append(contact2)

            return merged

        except Exception as e:
            self.logger.error(f"❌ 合并联系人结果失败: {e}")
            return contacts1

    def _filter_and_sort_contacts_optimized(self, contacts: List[Dict]) -> List[Dict]:
        """优化的联系人过滤和排序"""
        try:
            if not contacts:
                return []

            # 1. 基本过滤
            filtered = []
            for contact in contacts:
                coord = contact.get('coord')
                confidence = contact.get('confidence', 0)

                # 基本有效性检查
                if coord and len(coord) == 2 and confidence > 20:
                    filtered.append(contact)

            # 2. 去重（基于坐标相近性）
            deduplicated = []
            for contact in filtered:
                coord = contact['coord']
                is_duplicate = False

                for existing in deduplicated:
                    existing_coord = existing['coord']
                    distance = ((coord[0] - existing_coord[0]) ** 2 + (coord[1] - existing_coord[1]) ** 2) ** 0.5

                    if distance < 25:  # 25像素内认为重复
                        is_duplicate = True
                        # 保留置信度更高的
                        if contact['confidence'] > existing['confidence']:
                            deduplicated.remove(existing)
                            deduplicated.append(contact)
                        break

                if not is_duplicate:
                    deduplicated.append(contact)

            # 3. 按置信度和来源排序
            def sort_key(contact):
                confidence = contact.get('confidence', 0)
                source = contact.get('source', '')

                # 来源权重：轮廓检测 > 模板匹配 > OCR
                source_weight = 0
                if 'contour' in source:
                    source_weight = 100
                elif 'template' in source:
                    source_weight = 80
                elif 'ocr' in source:
                    source_weight = 60

                return confidence + source_weight

            deduplicated.sort(key=sort_key, reverse=True)

            # 4. 限制数量
            final_contacts = deduplicated[:8]  # 最多8个联系人

            self.logger.info(f"🔍 联系人过滤结果: 原始{len(contacts)} -> 过滤{len(filtered)} -> 去重{len(deduplicated)} -> 最终{len(final_contacts)}")

            return final_contacts

        except Exception as e:
            self.logger.error(f"❌ 联系人过滤排序失败: {e}")
            return contacts[:5]  # 出错时返回前5个

    def _filter_and_sort_contacts(self, contacts: List[Dict]) -> List[Dict]:
        """过滤和排序联系人结果"""
        try:
            # 过滤掉置信度过低的结果
            filtered = [c for c in contacts if c.get('confidence', 0) >= 30]

            # 按Y坐标排序（从上到下）
            filtered.sort(key=lambda x: x['coord'][1])

            # 限制最多返回10个联系人
            return filtered[:10]

        except Exception as e:
            self.logger.error(f"❌ 过滤排序联系人失败: {e}")
            return contacts

    def _save_contact_debug_info(self, screenshot: np.ndarray, contacts: List[Dict]):
        """保存联系人识别的调试信息"""
        try:
            if not self.debug_mode:
                return

            # 在截图上标记识别到的联系人
            debug_img = screenshot.copy()

            for i, contact in enumerate(contacts):
                coord = contact['coord']
                bbox = contact.get('bbox', (coord[0]-20, coord[1]-10, 40, 20))
                name = contact.get('name', f'联系人{i+1}')
                confidence = contact.get('confidence', 0)
                source = contact.get('source', 'unknown')

                # 绘制边界框
                x, y, w, h = bbox
                cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 255, 0), 2)

                # 绘制中心点
                cv2.circle(debug_img, coord, 5, (255, 0, 0), -1)

                # 添加标签（包含来源信息）
                label = f"{i+1}.{name}({confidence}%)[{source}]"
                cv2.putText(debug_img, label, (x, y-5), cv2.FONT_HERSHEY_SIMPLEX,
                           0.5, (255, 255, 0), 1)

            # 保存调试图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            debug_filename = f"contact_recognition_debug_{timestamp}.png"
            debug_path = os.path.join(self.screenshot_dir, debug_filename)

            cv2.imwrite(debug_path, debug_img)
            self.logger.info(f"💾 联系人识别调试图像已保存: {debug_path}")

        except Exception as e:
            self.logger.error(f"❌ 保存调试信息失败: {e}")

    def click_contact_by_image_recognition(self, contact_name: Optional[str] = None) -> bool:
        """
        使用图像识别点击联系人

        Args:
            contact_name: 指定要点击的联系人名称，如果为None则点击第一个识别到的联系人

        Returns:
            bool: 点击是否成功
        """
        try:
            self.logger.info(f"🔍 开始使用图像识别点击联系人: {contact_name or '第一个联系人'}")

            # 获取微信窗口截图
            screenshot = self.capture_wechat_window()
            if screenshot is None:
                self.logger.error("❌ 无法获取微信窗口截图")
                return False

            # 识别联系人
            contacts = self.find_contacts_in_screenshot(screenshot)
            if not contacts:
                self.logger.warning("⚠️ 未识别到任何联系人")
                return False

            # 选择要点击的联系人
            target_contact = None

            if contact_name:
                # 查找指定名称的联系人
                for contact in contacts:
                    if contact_name in contact.get('name', ''):
                        target_contact = contact
                        break

                if not target_contact:
                    self.logger.warning(f"⚠️ 未找到指定联系人: {contact_name}，将点击第一个联系人")
                    target_contact = contacts[0]
            else:
                # 点击第一个联系人
                target_contact = contacts[0]

            # 执行点击
            coord = target_contact['coord']
            name = target_contact.get('name', '未知联系人')

            self.logger.info(f"🎯 准备点击联系人: '{name}' 坐标: {coord}")

            # 点击联系人
            pyautogui.click(coord[0], coord[1])
            time.sleep(0.5)

            self.logger.info(f"✅ 成功点击联系人: '{name}'")
            return True

        except Exception as e:
            self.logger.error(f"❌ 图像识别点击联系人失败: {e}")
            return False


def test_contact_recognition():
    """测试联系人识别功能"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    print("🚀 开始测试微信联系人图像识别功能...")

    # 创建图像识别实例
    recognizer = WeChatImageRecognition(debug_mode=True)

    # 测试联系人识别
    print("\n📋 测试联系人识别...")
    screenshot = recognizer.capture_wechat_window()
    if screenshot is not None:
        contacts = recognizer.find_contacts_in_screenshot(screenshot)
        print(f"识别到 {len(contacts)} 个联系人:")
        for i, contact in enumerate(contacts):
            print(f"  {i+1}. {contact.get('name', '未知')} - {contact.get('coord')} ({contact.get('source')})")

    # 测试点击联系人
    print("\n🎯 测试点击联系人...")
    success = recognizer.click_contact_by_image_recognition()

    if success:
        print("✅ 联系人图像识别和点击测试成功！")
    else:
        print("❌ 联系人图像识别和点击测试失败！")


if __name__ == "__main__":
    # 可以选择测试不同功能
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "contact":
        test_contact_recognition()
    else:
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )

        # 创建实例并执行原有功能
        auto_add = WeChatImageRecognition(debug_mode=True)

        print("🚀 开始执行微信图像识别功能测试...")

        # 测试添加朋友窗口识别
        add_friend_window = auto_add.find_add_friend_window()
        if add_friend_window:
            print("✅ 找到添加朋友窗口")
            screenshot = auto_add.capture_window_screenshot(add_friend_window)
            if screenshot is not None:
                print("✅ 成功截取添加朋友窗口")
                # 这里可以添加按钮识别逻辑
                button_result = auto_add.find_button_by_image_processing(screenshot)
                if button_result:
                    print("✅ 找到添加到通讯录按钮")
                else:
                    print("⚠️ 未找到添加到通讯录按钮")
            else:
                print("❌ 截取添加朋友窗口失败")
        else:
            print("⚠️ 未找到添加朋友窗口，测试联系人识别功能")
            test_contact_recognition()

        print("\n💡 提示：使用 'python friend.py contact' 可以测试联系人识别功能")